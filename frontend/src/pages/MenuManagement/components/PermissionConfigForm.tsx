import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { X } from 'lucide-react';
import { PermissionFormProps, PERMISSION_MODE_OPTIONS } from '@/types/menu';

const PermissionConfigForm: React.FC<PermissionFormProps> = ({ 
  data, 
  templates, 
  onChange, 
  disabled 
}) => {
  const [permissionInput, setPermissionInput] = useState('');
  const [dataScopeInput, setDataScopeInput] = useState('');

  const addPermission = () => {
    if (permissionInput.trim()) {
      const currentPermissions = data.required_permissions || [];
      if (!currentPermissions.includes(permissionInput.trim())) {
        onChange({
          ...data,
          required_permissions: [...currentPermissions, permissionInput.trim()]
        });
      }
      setPermissionInput('');
    }
  };

  const removePermission = (permission: string) => {
    const currentPermissions = data.required_permissions || [];
    onChange({
      ...data,
      required_permissions: currentPermissions.filter(p => p !== permission)
    });
  };

  const addDataScope = () => {
    if (dataScopeInput.trim()) {
      const currentScopes = data.data_scopes || [];
      if (!currentScopes.includes(dataScopeInput.trim())) {
        onChange({
          ...data,
          data_scopes: [...currentScopes, dataScopeInput.trim()]
        });
      }
      setDataScopeInput('');
    }
  };

  const removeDataScope = (scope: string) => {
    const currentScopes = data.data_scopes || [];
    onChange({
      ...data,
      data_scopes: currentScopes.filter(s => s !== scope)
    });
  };

  return (
    <div className="space-y-6">
      {/* 权限模板选择 */}
      <div>
        <Label>权限模板</Label>
        <Select
          onValueChange={(templateId) => {
            const template = templates.find(t => t.id === templateId);
            if (template) {
              onChange({
                ...data,
                required_permissions: template.permissions,
                data_scopes: template.data_scopes,
                permission_mode: template.permission_mode
              });
            }
          }}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择权限模板（可选）" />
          </SelectTrigger>
          <SelectContent>
            {templates
              .filter(t => t.is_active)
              .map(template => (
                <SelectItem key={template.id} value={template.id}>
                  {template.template_name} ({template.template_type})
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      <Separator />

      {/* 权限配置 */}
      <div>
        <Label>所需权限</Label>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input
              value={permissionInput}
              onChange={(e) => setPermissionInput(e.target.value)}
              placeholder="输入权限，如：student:read"
              disabled={disabled}
              onKeyPress={(e) => e.key === 'Enter' && addPermission()}
            />
            <Button onClick={addPermission} disabled={disabled}>
              添加
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {(data.required_permissions || []).map((permission) => (
              <Badge key={permission} variant="secondary" className="flex items-center gap-1">
                {permission}
                {!disabled && (
                  <button
                    onClick={() => removePermission(permission)}
                    className="ml-1 text-red-500 hover:text-red-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 数据范围 */}
      <div>
        <Label>数据范围</Label>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input
              value={dataScopeInput}
              onChange={(e) => setDataScopeInput(e.target.value)}
              placeholder="输入数据范围，如：class:*"
              disabled={disabled}
              onKeyPress={(e) => e.key === 'Enter' && addDataScope()}
            />
            <Button onClick={addDataScope} disabled={disabled}>
              添加
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {(data.data_scopes || []).map((scope) => (
              <Badge key={scope} variant="outline" className="flex items-center gap-1">
                {scope}
                {!disabled && (
                  <button
                    onClick={() => removeDataScope(scope)}
                    className="ml-1 text-red-500 hover:text-red-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 权限模式 */}
      <div>
        <Label>权限模式</Label>
        <Select
          value={data.permission_mode || 'any'}
          onValueChange={(value) => onChange({ ...data, permission_mode: value })}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {PERMISSION_MODE_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-500 mt-1">
          ANY: 满足任一权限即可访问 | ALL: 必须满足所有权限
        </p>
      </div>
    </div>
  );
};

export default PermissionConfigForm;