import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Template,
  Shield,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Play,
  Copy,
  Download,
  Upload,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  AlertTriangle,
  Clock,
  Users,
  Settings,
  Eye,
  Zap,
  Target,
  TrendingUp,
  FileText,
  Layers
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { permissionTemplateApi, PermissionTemplate, TemplateCreateRequest, TemplateUpdateRequest, ApplyTemplateRequest } from '@/services/permissionTemplateApi';
import { adminMenuApi, MenuItem } from '@/services/adminMenuApi';

// 权限模板管理页面
const PermissionTemplateManagement: React.FC = () => {
  const [templates, setTemplates] = useState<PermissionTemplate[]>([]);
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<PermissionTemplate | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showApplyDialog, setShowApplyDialog] = useState(false);
  const [applyTargetMenus, setApplyTargetMenus] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const { toast } = useToast();

  // 加载权限模板数据
  const loadTemplates = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        search: searchTerm || undefined,
        template_type: filterType !== 'all' ? filterType : undefined,
        template_category: filterCategory !== 'all' ? filterCategory : undefined,
        is_active: filterStatus === 'active' ? true : filterStatus === 'inactive' ? false : undefined,
        page: currentPage,
        page_size: pageSize,
        sort_by: 'updated_at',
        sort_order: 'desc'
      };

      const response = await permissionTemplateApi.getTemplates(params);
      setTemplates(response.data);
      setTotalPages(Math.ceil(response.pagination.total / pageSize));
    } catch (error) {
      toast({
        title: "加载失败",
        description: "无法加载权限模板数据",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [searchTerm, filterType, filterCategory, filterStatus, currentPage, pageSize, toast]);

  // 加载菜单数据
  const loadMenus = useCallback(async () => {
    try {
      const response = await adminMenuApi.getMenuTree({ include_children: true });
      setMenus(response.data || []);
    } catch (error) {
      console.error('Failed to load menus:', error);
    }
  }, []);

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  useEffect(() => {
    loadMenus();
  }, [loadMenus]);

  // 过滤模板
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || template.template_type === filterType;
    const matchesCategory = filterCategory === 'all' || template.template_category === filterCategory;
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && template.is_active) ||
                         (filterStatus === 'inactive' && !template.is_active);
    
    return matchesSearch && matchesType && matchesCategory && matchesStatus;
  });

  // 保存模板
  const handleSaveTemplate = async (templateData: TemplateCreateRequest | TemplateUpdateRequest) => {
    try {
      if (isCreating) {
        await permissionTemplateApi.createTemplate(templateData as TemplateCreateRequest);
        toast({
          title: "创建成功",
          description: "权限模板创建成功",
        });
      } else if (selectedTemplate) {
        await permissionTemplateApi.updateTemplate(selectedTemplate.id, templateData as TemplateUpdateRequest);
        toast({
          title: "更新成功",
          description: "权限模板更新成功",
        });
      }
      
      setIsEditing(false);
      setIsCreating(false);
      setSelectedTemplate(null);
      loadTemplates();
    } catch (error) {
      toast({
        title: "保存失败",
        description: "权限模板保存失败，请重试",
        variant: "destructive",
      });
    }
  };

  // 删除模板
  const handleDeleteTemplate = async (template: PermissionTemplate) => {
    if (template.is_system_template) {
      toast({
        title: "操作失败",
        description: "系统模板无法删除",
        variant: "destructive",
      });
      return;
    }

    if (!confirm(`确定删除权限模板 "${template.template_name}" 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await permissionTemplateApi.deleteTemplate(template.id);
      toast({
        title: "删除成功",
        description: "权限模板已删除",
      });
      loadTemplates();
      if (selectedTemplate?.id === template.id) {
        setSelectedTemplate(null);
      }
    } catch (error) {
      toast({
        title: "删除失败",
        description: "权限模板删除失败，请重试",
        variant: "destructive",
      });
    }
  };

  // 应用模板
  const handleApplyTemplate = async () => {
    if (!selectedTemplate || applyTargetMenus.length === 0) {
      return;
    }

    try {
      const request: ApplyTemplateRequest = {
        template_id: selectedTemplate.id,
        menu_ids: applyTargetMenus,
        override_existing: true,
        reason: `应用权限模板: ${selectedTemplate.template_name}`
      };

      const response = await permissionTemplateApi.applyTemplate(request);
      const result = response.data;

      toast({
        title: "应用完成",
        description: `成功应用到 ${result.successful_count} 个菜单，失败 ${result.failed_count} 个`,
      });

      setShowApplyDialog(false);
      setApplyTargetMenus([]);
    } catch (error) {
      toast({
        title: "应用失败",
        description: "权限模板应用失败，请重试",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-full bg-gray-50">
      {/* 左侧模板列表 */}
      <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">权限模板</h2>
            <Button
              size="sm"
              onClick={() => {
                setSelectedTemplate(null);
                setIsCreating(true);
                setIsEditing(true);
              }}
            >
              <Plus className="w-4 h-4 mr-2" />
              新建模板
            </Button>
          </div>
          
          {/* 搜索和过滤 */}
          <div className="space-y-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="搜索模板..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="grid grid-cols-3 gap-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="role_based">基于角色</SelectItem>
                  <SelectItem value="resource_based">基于资源</SelectItem>
                  <SelectItem value="custom">自定义</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  <SelectItem value="admin">管理类</SelectItem>
                  <SelectItem value="functional">功能类</SelectItem>
                  <SelectItem value="data">数据类</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">启用</SelectItem>
                  <SelectItem value="inactive">禁用</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 模板列表 */}
        <div className="flex-1 overflow-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  isSelected={selectedTemplate?.id === template.id}
                  onSelect={() => setSelectedTemplate(template)}
                  onEdit={() => {
                    setSelectedTemplate(template);
                    setIsEditing(true);
                  }}
                  onDelete={() => handleDeleteTemplate(template)}
                  onApply={() => {
                    setSelectedTemplate(template);
                    setShowApplyDialog(true);
                  }}
                />
              ))}
            </div>
          )}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => prev - 1)}
              >
                上一页
              </Button>
              <span className="text-sm text-gray-500">
                {currentPage} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => prev + 1)}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 右侧配置面板 */}
      <div className="flex-1 flex flex-col">
        {selectedTemplate || isCreating ? (
          <TemplateConfigPanel
            template={selectedTemplate}
            isEditing={isEditing}
            isCreating={isCreating}
            onSave={handleSaveTemplate}
            onCancel={() => {
              setIsEditing(false);
              setIsCreating(false);
              setSelectedTemplate(null);
            }}
            onEdit={() => setIsEditing(true)}
            onApply={() => setShowApplyDialog(true)}
          />
        ) : (
          <EmptyState />
        )}
      </div>

      {/* 应用模板对话框 */}
      <Dialog open={showApplyDialog} onOpenChange={setShowApplyDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>应用权限模板</DialogTitle>
          </DialogHeader>
          <ApplyTemplateDialog
            template={selectedTemplate}
            menus={menus}
            selectedMenus={applyTargetMenus}
            onSelectionChange={setApplyTargetMenus}
            onApply={handleApplyTemplate}
            onCancel={() => setShowApplyDialog(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

// 模板卡片组件
const TemplateCard: React.FC<{
  template: PermissionTemplate;
  isSelected: boolean;
  onSelect: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onApply: () => void;
}> = ({ template, isSelected, onSelect, onEdit, onDelete, onApply }) => {
  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500 shadow-md' : ''
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-medium text-gray-900 truncate">
                {template.template_name}
              </h3>
              <div className="flex items-center gap-1">
                {template.is_system_template && (
                  <Badge variant="secondary" className="text-xs">
                    系统
                  </Badge>
                )}
                {!template.is_active && (
                  <Badge variant="outline" className="text-xs">
                    禁用
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
              <Badge variant="outline" className="text-xs">
                {template.template_type}
              </Badge>
              {template.template_category && (
                <Badge variant="outline" className="text-xs">
                  {template.template_category}
                </Badge>
              )}
            </div>
            
            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {template.description || '无描述'}
            </p>
            
            <div className="flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center gap-3">
                <span className="flex items-center gap-1">
                  <Shield className="w-3 h-3" />
                  {template.permissions.length}
                </span>
                <span className="flex items-center gap-1">
                  <Users className="w-3 h-3" />
                  {template.usage_count}
                </span>
              </div>
              <span>
                {new Date(template.updated_at).toLocaleDateString()}
              </span>
            </div>
          </div>
          
          <div className="flex flex-col gap-1 ml-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => { e.stopPropagation(); onEdit(); }}
              className="h-6 w-6 p-0"
            >
              <Edit className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => { e.stopPropagation(); onApply(); }}
              className="h-6 w-6 p-0"
            >
              <Play className="w-3 h-3" />
            </Button>
            {!template.is_system_template && (
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => { e.stopPropagation(); onDelete(); }}
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// 空状态组件
const EmptyState: React.FC = () => {
  return (
    <div className="flex-1 flex items-center justify-center bg-white">
      <div className="text-center">
        <Template className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          选择权限模板进行配置
        </h3>
        <p className="text-gray-500 mb-4">
          从左侧列表中选择一个权限模板来查看和编辑其配置信息
        </p>
        <div className="flex justify-center space-x-4 text-sm text-gray-400">
          <div className="flex items-center">
            <Shield className="w-4 h-4 mr-1" />
            权限配置
          </div>
          <div className="flex items-center">
            <Target className="w-4 h-4 mr-1" />
            模板应用
          </div>
          <div className="flex items-center">
            <TrendingUp className="w-4 h-4 mr-1" />
            使用统计
          </div>
        </div>
      </div>
    </div>
  );
};

// 模板配置面板组件
const TemplateConfigPanel: React.FC<{
  template: PermissionTemplate | null;
  isEditing: boolean;
  isCreating: boolean;
  onSave: (data: TemplateCreateRequest | TemplateUpdateRequest) => void;
  onCancel: () => void;
  onEdit: () => void;
  onApply: () => void;
}> = ({ template, isEditing, isCreating, onSave, onCancel, onEdit, onApply }) => {
  const [formData, setFormData] = useState<Partial<PermissionTemplate>>({});

  useEffect(() => {
    if (template) {
      setFormData(template);
    } else if (isCreating) {
      setFormData({
        template_name: '',
        template_type: 'role_based',
        template_category: 'functional',
        permissions: [],
        data_scopes: [],
        permission_mode: 'any',
        description: '',
        is_active: true,
        metadata: {}
      });
    }
  }, [template, isCreating]);

  const handleSave = () => {
    if (isCreating) {
      onSave({
        template_name: formData.template_name!,
        template_type: formData.template_type!,
        template_category: formData.template_category,
        permissions: formData.permissions!,
        data_scopes: formData.data_scopes,
        permission_mode: formData.permission_mode,
        description: formData.description,
        metadata: formData.metadata
      });
    } else {
      onSave({
        template_name: formData.template_name,
        template_type: formData.template_type,
        template_category: formData.template_category,
        permissions: formData.permissions,
        data_scopes: formData.data_scopes,
        permission_mode: formData.permission_mode,
        description: formData.description,
        is_active: formData.is_active,
        metadata: formData.metadata
      });
    }
  };

  return (
    <div className="flex-1 bg-white flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {isCreating ? '创建权限模板' : template?.template_name || '权限模板配置'}
            </h2>
            {template && (
              <p className="text-sm text-gray-500 mt-1">
                ID: {template.id} | 类型: {template.template_type} | 使用次数: {template.usage_count}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={onCancel}>
                  <X className="w-4 h-4 mr-2" />
                  取消
                </Button>
                <Button onClick={handleSave}>
                  <Save className="w-4 h-4 mr-2" />
                  保存
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" onClick={onApply} disabled={!template?.is_active}>
                  <Play className="w-4 h-4 mr-2" />
                  应用模板
                </Button>
                <Button onClick={onEdit} disabled={template?.is_system_template}>
                  <Edit className="w-4 h-4 mr-2" />
                  编辑
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 配置内容 */}
      <div className="flex-1 overflow-auto p-6">
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="permissions">权限配置</TabsTrigger>
            <TabsTrigger value="compatibility">兼容性</TabsTrigger>
            <TabsTrigger value="stats">使用统计</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <TemplateBasicForm
              data={formData}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <TemplatePermissionForm
              data={formData}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="compatibility" className="space-y-4">
            {template && (
              <TemplateCompatibilityView template={template} />
            )}
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            {template && (
              <TemplateUsageStats template={template} />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

// 基本信息表单
const TemplateBasicForm: React.FC<{
  data: Partial<PermissionTemplate>;
  onChange: (data: Partial<PermissionTemplate>) => void;
  disabled: boolean;
}> = ({ data, onChange, disabled }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="template_name">模板名称 *</Label>
          <Input
            id="template_name"
            value={data.template_name || ''}
            onChange={(e) => onChange({ ...data, template_name: e.target.value })}
            disabled={disabled}
            placeholder="输入模板名称"
          />
        </div>
        <div>
          <Label htmlFor="template_type">模板类型 *</Label>
          <Select
            value={data.template_type || 'role_based'}
            onValueChange={(value) => onChange({ ...data, template_type: value })}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="role_based">基于角色</SelectItem>
              <SelectItem value="resource_based">基于资源</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="template_category">模板分类</Label>
          <Select
            value={data.template_category || 'functional'}
            onValueChange={(value) => onChange({ ...data, template_category: value })}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="admin">管理类</SelectItem>
              <SelectItem value="functional">功能类</SelectItem>
              <SelectItem value="data">数据类</SelectItem>
              <SelectItem value="security">安全类</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="permission_mode">权限模式</Label>
          <Select
            value={data.permission_mode || 'any'}
            onValueChange={(value) => onChange({ ...data, permission_mode: value })}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="any">任一满足 (ANY)</SelectItem>
              <SelectItem value="all">全部满足 (ALL)</SelectItem>
              <SelectItem value="custom">自定义逻辑</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          value={data.description || ''}
          onChange={(e) => onChange({ ...data, description: e.target.value })}
          disabled={disabled}
          placeholder="模板功能描述"
          rows={3}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          checked={data.is_active ?? true}
          onCheckedChange={(checked) => onChange({ ...data, is_active: checked })}
          disabled={disabled}
        />
        <Label>启用模板</Label>
      </div>
    </div>
  );
};

// 权限配置表单
const TemplatePermissionForm: React.FC<{
  data: Partial<PermissionTemplate>;
  onChange: (data: Partial<PermissionTemplate>) => void;
  disabled: boolean;
}> = ({ data, onChange, disabled }) => {
  const [permissionInput, setPermissionInput] = useState('');
  const [dataScopeInput, setDataScopeInput] = useState('');

  const addPermission = () => {
    if (permissionInput.trim()) {
      const currentPermissions = data.permissions || [];
      if (!currentPermissions.includes(permissionInput.trim())) {
        onChange({
          ...data,
          permissions: [...currentPermissions, permissionInput.trim()]
        });
      }
      setPermissionInput('');
    }
  };

  const removePermission = (permission: string) => {
    const currentPermissions = data.permissions || [];
    onChange({
      ...data,
      permissions: currentPermissions.filter(p => p !== permission)
    });
  };

  const addDataScope = () => {
    if (dataScopeInput.trim()) {
      const currentScopes = data.data_scopes || [];
      if (!currentScopes.includes(dataScopeInput.trim())) {
        onChange({
          ...data,
          data_scopes: [...currentScopes, dataScopeInput.trim()]
        });
      }
      setDataScopeInput('');
    }
  };

  const removeDataScope = (scope: string) => {
    const currentScopes = data.data_scopes || [];
    onChange({
      ...data,
      data_scopes: currentScopes.filter(s => s !== scope)
    });
  };

  return (
    <div className="space-y-6">
      {/* 权限配置 */}
      <div>
        <Label>所需权限</Label>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input
              value={permissionInput}
              onChange={(e) => setPermissionInput(e.target.value)}
              placeholder="输入权限，如：student:read"
              disabled={disabled}
              onKeyPress={(e) => e.key === 'Enter' && addPermission()}
            />
            <Button onClick={addPermission} disabled={disabled}>
              添加
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {(data.permissions || []).map((permission) => (
              <Badge key={permission} variant="secondary" className="flex items-center gap-1">
                {permission}
                {!disabled && (
                  <button
                    onClick={() => removePermission(permission)}
                    className="ml-1 text-red-500 hover:text-red-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 数据范围 */}
      <div>
        <Label>数据范围</Label>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input
              value={dataScopeInput}
              onChange={(e) => setDataScopeInput(e.target.value)}
              placeholder="输入数据范围，如：class:*"
              disabled={disabled}
              onKeyPress={(e) => e.key === 'Enter' && addDataScope()}
            />
            <Button onClick={addDataScope} disabled={disabled}>
              添加
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {(data.data_scopes || []).map((scope) => (
              <Badge key={scope} variant="outline" className="flex items-center gap-1">
                {scope}
                {!disabled && (
                  <button
                    onClick={() => removeDataScope(scope)}
                    className="ml-1 text-red-500 hover:text-red-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 常用权限建议 */}
      <div>
        <Label>常用权限建议</Label>
        <div className="grid grid-cols-3 gap-2 mt-2">
          {[
            'student:read', 'student:write', 'class:read', 'class:write',
            'exam:read', 'exam:write', 'grade:read', 'grade:write',
            'user:read', 'user:write', 'admin:read', 'admin:write'
          ].map((permission) => (
            <Button
              key={permission}
              variant="outline"
              size="sm"
              disabled={disabled || (data.permissions || []).includes(permission)}
              onClick={() => {
                const currentPermissions = data.permissions || [];
                onChange({
                  ...data,
                  permissions: [...currentPermissions, permission]
                });
              }}
              className="text-xs"
            >
              + {permission}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

// 兼容性视图
const TemplateCompatibilityView: React.FC<{
  template: PermissionTemplate;
}> = ({ template }) => {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">兼容性检查</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-500">
            兼容性检查功能开发中...
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// 使用统计
const TemplateUsageStats: React.FC<{
  template: PermissionTemplate;
}> = ({ template }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {template.usage_count}
            </div>
            <div className="text-sm text-gray-600">总使用次数</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {template.permissions.length}
            </div>
            <div className="text-sm text-gray-600">权限数量</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">
              {template.data_scopes?.length || 0}
            </div>
            <div className="text-sm text-gray-600">数据范围</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">
              {template.is_active ? '启用' : '禁用'}
            </div>
            <div className="text-sm text-gray-600">当前状态</div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">最近使用</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-500">
            {template.last_used_at 
              ? `最后使用: ${new Date(template.last_used_at).toLocaleString()}`
              : '尚未使用过此模板'
            }
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// 应用模板对话框
const ApplyTemplateDialog: React.FC<{
  template: PermissionTemplate | null;
  menus: MenuItem[];
  selectedMenus: string[];
  onSelectionChange: (menuIds: string[]) => void;
  onApply: () => void;
  onCancel: () => void;
}> = ({ template, menus, selectedMenus, onSelectionChange, onApply, onCancel }) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredMenus = menus.filter(menu =>
    menu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    menu.menu_id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleMenuToggle = (menuId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedMenus, menuId]);
    } else {
      onSelectionChange(selectedMenus.filter(id => id !== menuId));
    }
  };

  const handleSelectAll = () => {
    if (selectedMenus.length === filteredMenus.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(filteredMenus.map(menu => menu.menu_id));
    }
  };

  if (!template) return null;

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium">应用模板: {template.template_name}</h3>
        <p className="text-sm text-gray-500">
          将此权限模板应用到选定的菜单项，这将覆盖原有的权限配置。
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Input
            placeholder="搜索菜单..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 mr-2"
          />
          <Button variant="outline" onClick={handleSelectAll}>
            {selectedMenus.length === filteredMenus.length ? '取消全选' : '全选'}
          </Button>
        </div>

        <div className="max-h-60 overflow-auto border rounded-lg p-2">
          {filteredMenus.map((menu) => (
            <div key={menu.menu_id} className="flex items-center space-x-2 py-2">
              <Checkbox
                checked={selectedMenus.includes(menu.menu_id)}
                onCheckedChange={(checked) => handleMenuToggle(menu.menu_id, checked as boolean)}
              />
              <div className="flex-1">
                <div className="font-medium">{menu.name}</div>
                <div className="text-sm text-gray-500">{menu.path}</div>
              </div>
              <Badge variant="outline" className="text-xs">
                {menu.menu_type}
              </Badge>
            </div>
          ))}
        </div>
      </div>

      <div className="text-sm text-gray-500">
        已选择 {selectedMenus.length} 个菜单
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button 
          onClick={onApply} 
          disabled={selectedMenus.length === 0}
        >
          应用模板
        </Button>
      </div>
    </div>
  );
};

export default PermissionTemplateManagement;