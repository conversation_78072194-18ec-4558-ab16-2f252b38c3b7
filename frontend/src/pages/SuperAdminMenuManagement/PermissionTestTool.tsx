import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { 
  TestTube,
  Play,
  User,
  Users,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  AlertTriangle,
  Download,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// 类型定义
interface PermissionTestResult {
  user_id?: string;
  menu_id: string;
  menu_name: string;
  accessible: boolean;
  reason?: string;
  required_permissions: string[];
  user_permissions: string[];
  matched_permissions: string[];
  missing_permissions: string[];
  data_scopes_valid: boolean;
  decision_path: string[];
  execution_time_ms: number;
}

interface BatchTestResponse {
  test_id?: string;
  test_name?: string;
  test_type: string;
  total_tests: number;
  passed_tests: number;
  failed_tests: number;
  execution_time_ms: number;
  results: UserTestResult[];
  summary: TestSummary;
}

interface UserTestResult {
  user_id: string;
  tenant_id: string;
  role_type?: string;
  total_menus_tested: number;
  accessible_menus: number;
  denied_menus: number;
  menu_results: PermissionTestResult[];
}

interface TestSummary {
  total_users: number;
  total_menus: number;
  overall_pass_rate: number;
  most_accessible_menu?: string;
  least_accessible_menu?: string;
  role_performance: RolePerformance[];
}

interface RolePerformance {
  role_type: string;
  total_tests: number;
  pass_rate: number;
  avg_accessible_menus: number;
}

interface TestHistoryRecord {
  id: string;
  test_name?: string;
  test_type: string;
  total_tests: number;
  passed_tests: number;
  failed_tests: number;
  execution_time_ms: number;
  tester_id: string;
  tester_identity: string;
  tenant_id?: string;
  created_at: string;
}

const PermissionTestTool: React.FC = () => {
  const [activeTab, setActiveTab] = useState('single-user');
  const [testResults, setTestResults] = useState<PermissionTestResult[]>([]);
  const [batchResults, setBatchResults] = useState<BatchTestResponse | null>(null);
  const [testHistory, setTestHistory] = useState<TestHistoryRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [testProgress, setTestProgress] = useState(0);
  const { toast } = useToast();

  // 加载测试历史
  const loadTestHistory = async () => {
    try {
      const response = await fetch('/api/v1/admin/permission-tests/history');
      if (response.ok) {
        const data = await response.json();
        setTestHistory(data.data || []);
      }
    } catch (error) {
      console.error('Failed to load test history:', error);
    }
  };

  useEffect(() => {
    loadTestHistory();
  }, []);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">权限测试工具</h1>
          <p className="text-gray-600 mt-1">
            测试和验证菜单权限配置的正确性
          </p>
        </div>
        <Button onClick={loadTestHistory} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="single-user">单用户测试</TabsTrigger>
          <TabsTrigger value="batch-users">批量用户测试</TabsTrigger>
          <TabsTrigger value="role-simulation">角色模拟测试</TabsTrigger>
          <TabsTrigger value="test-history">测试历史</TabsTrigger>
        </TabsList>

        <TabsContent value="single-user" className="space-y-6">
          <SingleUserTest
            onTestStart={() => setLoading(true)}
            onTestComplete={(results) => {
              setTestResults(results);
              setLoading(false);
            }}
            loading={loading}
          />
          {testResults.length > 0 && (
            <TestResults results={testResults} />
          )}
        </TabsContent>

        <TabsContent value="batch-users" className="space-y-6">
          <BatchUserTest
            onTestStart={() => {
              setLoading(true);
              setTestProgress(0);
            }}
            onTestProgress={(progress) => setTestProgress(progress)}
            onTestComplete={(results) => {
              setBatchResults(results);
              setLoading(false);
              setTestProgress(100);
            }}
            loading={loading}
            progress={testProgress}
          />
          {batchResults && (
            <BatchTestResults results={batchResults} />
          )}
        </TabsContent>

        <TabsContent value="role-simulation" className="space-y-6">
          <RoleSimulationTest
            onTestStart={() => setLoading(true)}
            onTestComplete={(results) => {
              setTestResults(results);
              setLoading(false);
            }}
            loading={loading}
          />
          {testResults.length > 0 && (
            <TestResults results={testResults} />
          )}
        </TabsContent>

        <TabsContent value="test-history" className="space-y-6">
          <TestHistory history={testHistory} onRefresh={loadTestHistory} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// 单用户测试组件
const SingleUserTest: React.FC<{
  onTestStart: () => void;
  onTestComplete: (results: PermissionTestResult[]) => void;
  loading: boolean;
}> = ({ onTestStart, onTestComplete, loading }) => {
  const [userId, setUserId] = useState('');
  const [tenantId, setTenantId] = useState('');
  const [roleType, setRoleType] = useState('');
  const [menuIds, setMenuIds] = useState('');
  const [testName, setTestName] = useState('');
  const [saveResult, setSaveResult] = useState(true);
  const { toast } = useToast();

  const handleTest = async () => {
    if (!userId || !tenantId || !menuIds) {
      toast({
        title: "参数错误",
        description: "请填写用户ID、租户ID和菜单ID",
        variant: "destructive",
      });
      return;
    }

    onTestStart();

    try {
      const menuIdList = menuIds.split(',').map(id => id.trim()).filter(Boolean);
      
      const response = await fetch('/api/v1/admin/permission-tests/single-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          tenant_id: tenantId,
          role_type: roleType || undefined,
          menu_ids: menuIdList,
          test_name: testName || undefined,
          save_result: saveResult,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        onTestComplete(data.data);
        toast({
          title: "测试完成",
          description: `测试了 ${data.data.length} 个菜单`,
        });
      } else {
        throw new Error('Test failed');
      }
    } catch (error) {
      toast({
        title: "测试失败",
        description: "无法执行权限测试",
        variant: "destructive",
      });
      onTestComplete([]);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="w-5 h-5 mr-2" />
          单用户权限测试
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="user-id">用户ID *</Label>
            <Input
              id="user-id"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="输入用户UUID"
              disabled={loading}
            />
          </div>
          <div>
            <Label htmlFor="tenant-id">租户ID *</Label>
            <Input
              id="tenant-id"
              value={tenantId}
              onChange={(e) => setTenantId(e.target.value)}
              placeholder="输入租户ID"
              disabled={loading}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="role-type">角色类型</Label>
            <Select value={roleType} onValueChange={setRoleType} disabled={loading}>
              <SelectTrigger>
                <SelectValue placeholder="选择角色类型（可选）" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">自动检测</SelectItem>
                <SelectItem value="super_admin">超级管理员</SelectItem>
                <SelectItem value="tenant_admin">租户管理员</SelectItem>
                <SelectItem value="principal">校长</SelectItem>
                <SelectItem value="teacher">教师</SelectItem>
                <SelectItem value="student">学生</SelectItem>
                <SelectItem value="parent">家长</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="test-name">测试名称</Label>
            <Input
              id="test-name"
              value={testName}
              onChange={(e) => setTestName(e.target.value)}
              placeholder="测试名称（可选）"
              disabled={loading}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="menu-ids">菜单ID列表 *</Label>
          <Textarea
            id="menu-ids"
            value={menuIds}
            onChange={(e) => setMenuIds(e.target.value)}
            placeholder="输入菜单ID，用逗号分隔，如：student_management,exam_management"
            rows={3}
            disabled={loading}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              checked={saveResult}
              onCheckedChange={setSaveResult}
              disabled={loading}
            />
            <Label>保存测试结果</Label>
          </div>
          <Button onClick={handleTest} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                测试中...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                开始测试
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// 批量用户测试组件
const BatchUserTest: React.FC<{
  onTestStart: () => void;
  onTestProgress: (progress: number) => void;
  onTestComplete: (results: BatchTestResponse) => void;
  loading: boolean;
  progress: number;
}> = ({ onTestStart, onTestProgress, onTestComplete, loading, progress }) => {
  const [userSpecs, setUserSpecs] = useState('');
  const [testName, setTestName] = useState('');
  const [parallelExecution, setParallelExecution] = useState(true);
  const [saveResult, setSaveResult] = useState(true);
  const { toast } = useToast();

  const handleTest = async () => {
    if (!userSpecs) {
      toast({
        title: "参数错误",
        description: "请输入用户测试规格",
        variant: "destructive",
      });
      return;
    }

    onTestStart();

    try {
      // 解析用户测试规格
      const lines = userSpecs.split('\n').filter(line => line.trim());
      const userTests = lines.map(line => {
        const [userId, tenantId, roleType, menuIds] = line.split('|').map(s => s.trim());
        return {
          user_id: userId,
          tenant_id: tenantId,
          role_type: roleType || undefined,
          menu_ids: menuIds.split(',').map(id => id.trim()).filter(Boolean),
        };
      });

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        onTestProgress(prev => Math.min(prev + 10, 90));
      }, 500);

      const response = await fetch('/api/v1/admin/permission-tests/batch-users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_tests: userTests,
          test_name: testName || undefined,
          parallel_execution: parallelExecution,
          save_result: saveResult,
        }),
      });

      clearInterval(progressInterval);

      if (response.ok) {
        const data = await response.json();
        onTestComplete(data.data);
        toast({
          title: "批量测试完成",
          description: `测试了 ${data.data.total_tests} 个权限点`,
        });
      } else {
        throw new Error('Batch test failed');
      }
    } catch (error) {
      toast({
        title: "测试失败",
        description: "无法执行批量权限测试",
        variant: "destructive",
      });
      onTestComplete({
        test_type: 'batch_users',
        total_tests: 0,
        passed_tests: 0,
        failed_tests: 0,
        execution_time_ms: 0,
        results: [],
        summary: {
          total_users: 0,
          total_menus: 0,
          overall_pass_rate: 0,
          role_performance: [],
        },
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="w-5 h-5 mr-2" />
          批量用户权限测试
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="user-specs">用户测试规格 *</Label>
          <Textarea
            id="user-specs"
            value={userSpecs}
            onChange={(e) => setUserSpecs(e.target.value)}
            placeholder={`格式：用户ID|租户ID|角色类型|菜单ID列表
示例：
user1|tenant_001|teacher|student_management,exam_management
user2|tenant_001|student|personal_center,my_grades`}
            rows={8}
            disabled={loading}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="batch-test-name">测试名称</Label>
            <Input
              id="batch-test-name"
              value={testName}
              onChange={(e) => setTestName(e.target.value)}
              placeholder="批量测试名称（可选）"
              disabled={loading}
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                checked={parallelExecution}
                onCheckedChange={setParallelExecution}
                disabled={loading}
              />
              <Label>并行执行</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={saveResult}
                onCheckedChange={setSaveResult}
                disabled={loading}
              />
              <Label>保存测试结果</Label>
            </div>
          </div>
          <Button onClick={handleTest} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                测试中...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                开始批量测试
              </>
            )}
          </Button>
        </div>

        {loading && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>测试进度</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// 角色模拟测试组件
const RoleSimulationTest: React.FC<{
  onTestStart: () => void;
  onTestComplete: (results: PermissionTestResult[]) => void;
  loading: boolean;
}> = ({ onTestStart, onTestComplete, loading }) => {
  const [roleType, setRoleType] = useState('');
  const [tenantId, setTenantId] = useState('');
  const [menuIds, setMenuIds] = useState('');
  const [testName, setTestName] = useState('');
  const [testAllMenus, setTestAllMenus] = useState(true);
  const [includeInheritance, setIncludeInheritance] = useState(true);
  const [saveResult, setSaveResult] = useState(true);
  const { toast } = useToast();

  const handleTest = async () => {
    if (!roleType || !tenantId) {
      toast({
        title: "参数错误",
        description: "请选择角色类型和租户ID",
        variant: "destructive",
      });
      return;
    }

    onTestStart();

    try {
      const menuIdList = testAllMenus ? undefined : 
        menuIds.split(',').map(id => id.trim()).filter(Boolean);
      
      const response = await fetch('/api/v1/admin/permission-tests/role-simulation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role_type: roleType,
          tenant_id: tenantId,
          menu_ids: menuIdList,
          test_name: testName || undefined,
          include_inheritance: includeInheritance,
          save_result: saveResult,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        onTestComplete(data.data);
        toast({
          title: "角色模拟测试完成",
          description: `测试了 ${data.data.length} 个菜单`,
        });
      } else {
        throw new Error('Role simulation test failed');
      }
    } catch (error) {
      toast({
        title: "测试失败",
        description: "无法执行角色模拟测试",
        variant: "destructive",
      });
      onTestComplete([]);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          角色模拟测试
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="sim-role-type">角色类型 *</Label>
            <Select value={roleType} onValueChange={setRoleType} disabled={loading}>
              <SelectTrigger>
                <SelectValue placeholder="选择要模拟的角色" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="super_admin">超级管理员</SelectItem>
                <SelectItem value="tenant_admin">租户管理员</SelectItem>
                <SelectItem value="principal">校长</SelectItem>
                <SelectItem value="academic_director">教导主任</SelectItem>
                <SelectItem value="teacher">教师</SelectItem>
                <SelectItem value="student">学生</SelectItem>
                <SelectItem value="parent">家长</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="sim-tenant-id">租户ID *</Label>
            <Input
              id="sim-tenant-id"
              value={tenantId}
              onChange={(e) => setTenantId(e.target.value)}
              placeholder="输入租户ID"
              disabled={loading}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="sim-test-name">测试名称</Label>
          <Input
            id="sim-test-name"
            value={testName}
            onChange={(e) => setTestName(e.target.value)}
            placeholder="角色模拟测试名称（可选）"
            disabled={loading}
          />
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Switch
              checked={testAllMenus}
              onCheckedChange={setTestAllMenus}
              disabled={loading}
            />
            <Label>测试所有菜单</Label>
          </div>

          {!testAllMenus && (
            <div>
              <Label htmlFor="sim-menu-ids">指定菜单ID</Label>
              <Textarea
                id="sim-menu-ids"
                value={menuIds}
                onChange={(e) => setMenuIds(e.target.value)}
                placeholder="输入菜单ID，用逗号分隔"
                rows={3}
                disabled={loading}
              />
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Switch
              checked={includeInheritance}
              onCheckedChange={setIncludeInheritance}
              disabled={loading}
            />
            <Label>包含权限继承</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={saveResult}
              onCheckedChange={setSaveResult}
              disabled={loading}
            />
            <Label>保存测试结果</Label>
          </div>
        </div>

        <div className="flex justify-end">
          <Button onClick={handleTest} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                模拟测试中...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                开始模拟测试
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// 测试结果组件
const TestResults: React.FC<{
  results: PermissionTestResult[];
}> = ({ results }) => {
  const passedCount = results.filter(r => r.accessible).length;
  const failedCount = results.length - passedCount;
  const passRate = results.length > 0 ? (passedCount / results.length) * 100 : 0;

  const exportResults = () => {
    const csvContent = [
      ['菜单ID', '菜单名称', '可访问', '原因', '所需权限', '用户权限', '执行时间(ms)'].join(','),
      ...results.map(result => [
        result.menu_id,
        result.menu_name,
        result.accessible ? '是' : '否',
        result.reason || '',
        result.required_permissions.join(';'),
        result.user_permissions.join(';'),
        result.execution_time_ms.toString()
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `permission_test_results_${new Date().toISOString()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>测试结果</CardTitle>
          <Button onClick={exportResults} variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            导出CSV
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 统计概览 */}
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{results.length}</div>
            <div className="text-sm text-gray-600">总测试数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{passedCount}</div>
            <div className="text-sm text-gray-600">通过</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{failedCount}</div>
            <div className="text-sm text-gray-600">失败</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{passRate.toFixed(1)}%</div>
            <div className="text-sm text-gray-600">通过率</div>
          </div>
        </div>

        {/* 详细结果 */}
        <div className="space-y-2 max-h-96 overflow-auto">
          {results.map((result, index) => (
            <div
              key={`${result.menu_id}-${index}`}
              className={`p-3 rounded border ${
                result.accessible ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center space-x-2">
                    {result.accessible ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-600" />
                    )}
                    <span className="font-medium">{result.menu_name}</span>
                    <Badge variant="outline" className="text-xs">
                      {result.menu_id}
                    </Badge>
                  </div>
                  {result.reason && (
                    <div className="text-sm text-gray-600 mt-1">
                      原因: {result.reason}
                    </div>
                  )}
                </div>
                <div className="text-right text-sm text-gray-500">
                  <div className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {result.execution_time_ms}ms
                  </div>
                </div>
              </div>

              {/* 权限详情 */}
              <div className="mt-2 space-y-1 text-xs">
                <div>
                  <span className="text-gray-600">所需权限: </span>
                  {result.required_permissions.map(perm => (
                    <Badge key={perm} variant="secondary" className="text-xs mr-1">
                      {perm}
                    </Badge>
                  ))}
                </div>
                {result.matched_permissions.length > 0 && (
                  <div>
                    <span className="text-gray-600">匹配权限: </span>
                    {result.matched_permissions.map(perm => (
                      <Badge key={perm} variant="default" className="text-xs mr-1 bg-green-100 text-green-800">
                        {perm}
                      </Badge>
                    ))}
                  </div>
                )}
                {result.missing_permissions.length > 0 && (
                  <div>
                    <span className="text-gray-600">缺失权限: </span>
                    {result.missing_permissions.map(perm => (
                      <Badge key={perm} variant="default" className="text-xs mr-1 bg-red-100 text-red-800">
                        {perm}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// 批量测试结果组件
const BatchTestResults: React.FC<{
  results: BatchTestResponse;
}> = ({ results }) => {
  return (
    <div className="space-y-6">
      {/* 概览统计 */}
      <Card>
        <CardHeader>
          <CardTitle>批量测试概览</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{results.summary.total_users}</div>
              <div className="text-sm text-gray-600">用户数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{results.summary.total_menus}</div>
              <div className="text-sm text-gray-600">菜单数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{results.passed_tests}</div>
              <div className="text-sm text-gray-600">通过测试</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{results.failed_tests}</div>
              <div className="text-sm text-gray-600">失败测试</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{results.summary.overall_pass_rate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">总体通过率</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 角色性能 */}
      {results.summary.role_performance.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>角色性能分析</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {results.summary.role_performance.map((role) => (
                <div key={role.role_type} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium">{role.role_type}</div>
                    <div className="text-sm text-gray-600">
                      平均可访问菜单: {role.avg_accessible_menus.toFixed(1)}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold">{role.pass_rate.toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">{role.total_tests} 次测试</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 用户结果 */}
      <Card>
        <CardHeader>
          <CardTitle>用户测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-auto">
            {results.results.map((userResult) => (
              <div key={userResult.user_id} className="border rounded p-4">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <div className="font-medium">用户: {userResult.user_id}</div>
                    <div className="text-sm text-gray-600">
                      租户: {userResult.tenant_id} | 角色: {userResult.role_type || '未指定'}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-green-600">
                      {userResult.accessible_menus}/{userResult.total_menus_tested}
                    </div>
                    <div className="text-sm text-gray-600">可访问菜单</div>
                  </div>
                </div>
                <Progress 
                  value={(userResult.accessible_menus / userResult.total_menus_tested) * 100} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// 测试历史组件
const TestHistory: React.FC<{
  history: TestHistoryRecord[];
  onRefresh: () => void;
}> = ({ history, onRefresh }) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>测试历史</CardTitle>
          <Button onClick={onRefresh} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {history.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              暂无测试历史记录
            </div>
          ) : (
            history.map((record) => (
              <div key={record.id} className="border rounded p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">
                      {record.test_name || `${record.test_type} 测试`}
                    </div>
                    <div className="text-sm text-gray-600">
                      {record.tester_identity} | {new Date(record.created_at).toLocaleString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-green-600">
                        通过: {record.passed_tests}
                      </span>
                      <span className="text-red-600">
                        失败: {record.failed_tests}
                      </span>
                      <span className="text-gray-500">
                        <Clock className="w-3 h-3 inline mr-1" />
                        {record.execution_time_ms}ms
                      </span>
                    </div>
                  </div>
                </div>
                <div className="mt-2">
                  <Progress 
                    value={(record.passed_tests / record.total_tests) * 100} 
                    className="h-1"
                  />
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PermissionTestTool;