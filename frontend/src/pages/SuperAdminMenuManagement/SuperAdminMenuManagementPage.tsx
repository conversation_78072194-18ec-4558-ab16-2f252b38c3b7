import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  <PERSON>,
  <PERSON>u,
  <PERSON>ting<PERSON>,
  Plus,
  <PERSON>,
  Trash2,
  Save,
  X,
  Shield,
  Eye,
  EyeOff,
  Users,
  TestTube,
  FileText,
  ChevronRight,
  ChevronDown,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// 类型定义
interface MenuItem {
  id?: string;
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_permissions: string[];
  data_scopes?: string[];
  permission_mode: string;
  access_level: number;
  sort_order: number;
  is_active: boolean;
  cache_enabled: boolean;
  metadata?: any;
  version: number;
  children?: MenuItem[];
  children_count: number;
  depth_level: number;
  usage_stats?: {
    total_access_count: number;
    unique_user_count: number;
    denied_access_count: number;
    last_accessed_at?: string;
    avg_daily_access: number;
  };
}

interface PermissionTemplate {
  id: string;
  template_name: string;
  template_type: string;
  template_category?: string;
  permissions: string[];
  data_scopes?: string[];
  permission_mode: string;
  description?: string;
  usage_count: number;
  is_system_template: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 主组件
const SuperAdminMenuManagement: React.FC = () => {
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [templates, setTemplates] = useState<PermissionTemplate[]>([]);
  const [selectedMenu, setSelectedMenu] = useState<MenuItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  // 加载菜单数据
  const loadMenus = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/admin/menus?include_children=true&include_metadata=true');
      if (response.ok) {
        const data = await response.json();
        setMenus(data.data || []);
      } else {
        throw new Error('Failed to load menus');
      }
    } catch (error) {
      toast({
        title: "加载失败",
        description: "无法加载菜单数据",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // 加载权限模板
  const loadTemplates = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/admin/permission-templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.data || []);
      }
    } catch (error) {
      toast({
        title: "加载失败",
        description: "无法加载权限模板",
        variant: "destructive",
      });
    }
  }, [toast]);

  useEffect(() => {
    loadMenus();
    loadTemplates();
  }, [loadMenus, loadTemplates]);

  // 过滤菜单
  const filteredMenus = menus.filter(menu => {
    const matchesSearch = menu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         menu.menu_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         menu.path.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || menu.menu_type === filterType;
    return matchesSearch && matchesType;
  });

  // 切换节点展开状态
  const toggleNode = (menuId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(menuId)) {
      newExpanded.delete(menuId);
    } else {
      newExpanded.add(menuId);
    }
    setExpandedNodes(newExpanded);
  };

  // 菜单树渲染
  const renderMenuTree = (items: MenuItem[], level = 0) => {
    return (
      <div className="space-y-1">
        {items.map((menu) => (
          <MenuTreeNode
            key={menu.menu_id}
            menu={menu}
            level={level}
            isExpanded={expandedNodes.has(menu.menu_id)}
            isSelected={selectedMenu?.menu_id === menu.menu_id}
            onToggle={() => toggleNode(menu.menu_id)}
            onSelect={() => setSelectedMenu(menu)}
            onEdit={() => {
              setSelectedMenu(menu);
              setIsEditing(true);
            }}
            onDelete={() => handleDeleteMenu(menu.menu_id)}
          />
        ))}
      </div>
    );
  };

  // 保存菜单
  const handleSaveMenu = async (menuData: Partial<MenuItem>) => {
    try {
      const url = isCreating 
        ? '/api/v1/admin/menus'
        : `/api/v1/admin/menus/${selectedMenu?.menu_id}`;
      
      const method = isCreating ? 'POST' : 'PUT';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(menuData),
      });

      if (response.ok) {
        toast({
          title: "保存成功",
          description: `菜单${isCreating ? '创建' : '更新'}成功`,
        });
        setIsEditing(false);
        setIsCreating(false);
        setSelectedMenu(null);
        loadMenus();
      } else {
        throw new Error('Save failed');
      }
    } catch (error) {
      toast({
        title: "保存失败",
        description: "菜单保存失败，请重试",
        variant: "destructive",
      });
    }
  };

  // 删除菜单
  const handleDeleteMenu = async (menuId: string) => {
    if (!confirm('确定删除此菜单吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/admin/menus/${menuId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: "删除成功",
          description: "菜单已删除",
        });
        loadMenus();
        if (selectedMenu?.menu_id === menuId) {
          setSelectedMenu(null);
        }
      } else {
        throw new Error('Delete failed');
      }
    } catch (error) {
      toast({
        title: "删除失败",
        description: "菜单删除失败，请重试",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-full bg-gray-50">
      {/* 左侧菜单树 */}
      <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">菜单结构</h2>
            <Button
              size="sm"
              onClick={() => {
                setSelectedMenu(null);
                setIsCreating(true);
                setIsEditing(true);
              }}
            >
              <Plus className="w-4 h-4 mr-2" />
              新建菜单
            </Button>
          </div>
          
          {/* 搜索和过滤 */}
          <div className="space-y-2">
            <Input
              placeholder="搜索菜单..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger>
                <SelectValue placeholder="选择菜单类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="functional">功能菜单</SelectItem>
                <SelectItem value="admin">管理菜单</SelectItem>
                <SelectItem value="personal">个人菜单</SelectItem>
                <SelectItem value="system">系统菜单</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 菜单树 */}
        <div className="flex-1 overflow-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : (
            renderMenuTree(filteredMenus)
          )}
        </div>
      </div>

      {/* 右侧配置面板 */}
      <div className="flex-1 flex flex-col">
        {selectedMenu || isCreating ? (
          <MenuConfigPanel
            menu={selectedMenu}
            templates={templates}
            isEditing={isEditing}
            isCreating={isCreating}
            onSave={handleSaveMenu}
            onCancel={() => {
              setIsEditing(false);
              setIsCreating(false);
              setSelectedMenu(null);
            }}
            onEdit={() => setIsEditing(true)}
          />
        ) : (
          <EmptyState />
        )}
      </div>
    </div>
  );
};

// 菜单树节点组件
const MenuTreeNode: React.FC<{
  menu: MenuItem;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  onToggle: () => void;
  onSelect: () => void;
  onEdit: () => void;
  onDelete: () => void;
}> = ({ menu, level, isExpanded, isSelected, onToggle, onSelect, onEdit, onDelete }) => {
  const hasChildren = menu.children_count > 0;
  const paddingLeft = level * 20 + 8;

  return (
    <div>
      <div
        className={`flex items-center p-2 rounded cursor-pointer hover:bg-gray-50 ${
          isSelected ? 'bg-blue-50 border border-blue-200' : ''
        }`}
        style={{ paddingLeft }}
        onClick={onSelect}
      >
        {/* 展开/收起图标 */}
        <div className="w-4 h-4 mr-2">
          {hasChildren && (
            <button onClick={(e) => { e.stopPropagation(); onToggle(); }}>
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {/* 菜单图标 */}
        <div className="w-4 h-4 mr-2 text-gray-400">
          <Menu className="w-4 h-4" />
        </div>

        {/* 菜单信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900 truncate">
              {menu.name}
            </span>
            <div className="ml-2 flex items-center space-x-1">
              {!menu.is_active && (
                <Badge variant="secondary" className="text-xs">
                  禁用
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                {menu.menu_type}
              </Badge>
            </div>
          </div>
          <div className="text-xs text-gray-500 truncate">
            {menu.path}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100">
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => { e.stopPropagation(); onEdit(); }}
          >
            <Edit className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => { e.stopPropagation(); onDelete(); }}
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* 子菜单 */}
      {hasChildren && isExpanded && menu.children && (
        <div className="ml-4">
          {menu.children.map((child) => (
            <MenuTreeNode
              key={child.menu_id}
              menu={child}
              level={level + 1}
              isExpanded={false}
              isSelected={false}
              onToggle={() => {}}
              onSelect={() => {}}
              onEdit={() => {}}
              onDelete={() => {}}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// 空状态组件
const EmptyState: React.FC = () => {
  return (
    <div className="flex-1 flex items-center justify-center bg-white">
      <div className="text-center">
        <Menu className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          选择菜单进行配置
        </h3>
        <p className="text-gray-500 mb-4">
          从左侧菜单树中选择一个菜单项来查看和编辑其权限配置
        </p>
        <div className="flex justify-center space-x-4 text-sm text-gray-400">
          <div className="flex items-center">
            <Tree className="w-4 h-4 mr-1" />
            菜单结构管理
          </div>
          <div className="flex items-center">
            <Shield className="w-4 h-4 mr-1" />
            权限配置
          </div>
          <div className="flex items-center">
            <TestTube className="w-4 h-4 mr-1" />
            权限测试
          </div>
        </div>
      </div>
    </div>
  );
};

// 菜单配置面板组件
const MenuConfigPanel: React.FC<{
  menu: MenuItem | null;
  templates: PermissionTemplate[];
  isEditing: boolean;
  isCreating: boolean;
  onSave: (data: Partial<MenuItem>) => void;
  onCancel: () => void;
  onEdit: () => void;
}> = ({ menu, templates, isEditing, isCreating, onSave, onCancel, onEdit }) => {
  const [formData, setFormData] = useState<Partial<MenuItem>>({});

  useEffect(() => {
    if (menu) {
      setFormData(menu);
    } else if (isCreating) {
      setFormData({
        menu_id: '',
        name: '',
        path: '',
        icon: '',
        parent_id: '',
        menu_type: 'functional',
        description: '',
        required_permissions: [],
        data_scopes: [],
        permission_mode: 'any',
        access_level: 0,
        sort_order: 0,
        is_active: true,
        cache_enabled: true,
      });
    }
  }, [menu, isCreating]);

  const handleSave = () => {
    onSave(formData);
  };

  return (
    <div className="flex-1 bg-white flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {isCreating ? '创建菜单' : menu?.name || '菜单配置'}
            </h2>
            {menu && (
              <p className="text-sm text-gray-500 mt-1">
                ID: {menu.menu_id} | 路径: {menu.path}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={onCancel}>
                  <X className="w-4 h-4 mr-2" />
                  取消
                </Button>
                <Button onClick={handleSave}>
                  <Save className="w-4 h-4 mr-2" />
                  保存
                </Button>
              </>
            ) : (
              <Button onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                编辑
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 配置内容 */}
      <div className="flex-1 overflow-auto p-6">
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="permissions">权限配置</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
            <TabsTrigger value="stats">使用统计</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <BasicInfoForm
              data={formData}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <PermissionConfigForm
              data={formData}
              templates={templates}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <AdvancedSettingsForm
              data={formData}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            {menu?.usage_stats && (
              <UsageStatistics stats={menu.usage_stats} />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

// 基本信息表单
const BasicInfoForm: React.FC<{
  data: Partial<MenuItem>;
  onChange: (data: Partial<MenuItem>) => void;
  disabled: boolean;
}> = ({ data, onChange, disabled }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="menu_id">菜单ID *</Label>
          <Input
            id="menu_id"
            value={data.menu_id || ''}
            onChange={(e) => onChange({ ...data, menu_id: e.target.value })}
            disabled={disabled}
            placeholder="输入菜单ID"
          />
        </div>
        <div>
          <Label htmlFor="name">菜单名称 *</Label>
          <Input
            id="name"
            value={data.name || ''}
            onChange={(e) => onChange({ ...data, name: e.target.value })}
            disabled={disabled}
            placeholder="输入菜单名称"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="path">路由路径 *</Label>
          <Input
            id="path"
            value={data.path || ''}
            onChange={(e) => onChange({ ...data, path: e.target.value })}
            disabled={disabled}
            placeholder="/path/to/page"
          />
        </div>
        <div>
          <Label htmlFor="icon">图标</Label>
          <Input
            id="icon"
            value={data.icon || ''}
            onChange={(e) => onChange({ ...data, icon: e.target.value })}
            disabled={disabled}
            placeholder="menu, settings, user"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="menu_type">菜单类型</Label>
          <Select
            value={data.menu_type || 'functional'}
            onValueChange={(value) => onChange({ ...data, menu_type: value })}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="functional">功能菜单</SelectItem>
              <SelectItem value="admin">管理菜单</SelectItem>
              <SelectItem value="personal">个人菜单</SelectItem>
              <SelectItem value="system">系统菜单</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="parent_id">父菜单ID</Label>
          <Input
            id="parent_id"
            value={data.parent_id || ''}
            onChange={(e) => onChange({ ...data, parent_id: e.target.value })}
            disabled={disabled}
            placeholder="父菜单ID（可选）"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          value={data.description || ''}
          onChange={(e) => onChange({ ...data, description: e.target.value })}
          disabled={disabled}
          placeholder="菜单功能描述"
          rows={3}
        />
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Switch
            checked={data.is_active ?? true}
            onCheckedChange={(checked) => onChange({ ...data, is_active: checked })}
            disabled={disabled}
          />
          <Label>启用菜单</Label>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            checked={data.cache_enabled ?? true}
            onCheckedChange={(checked) => onChange({ ...data, cache_enabled: checked })}
            disabled={disabled}
          />
          <Label>启用缓存</Label>
        </div>
      </div>
    </div>
  );
};

// 权限配置表单
const PermissionConfigForm: React.FC<{
  data: Partial<MenuItem>;
  templates: PermissionTemplate[];
  onChange: (data: Partial<MenuItem>) => void;
  disabled: boolean;
}> = ({ data, templates, onChange, disabled }) => {
  const [permissionInput, setPermissionInput] = useState('');
  const [dataScopeInput, setDataScopeInput] = useState('');

  const addPermission = () => {
    if (permissionInput.trim()) {
      const currentPermissions = data.required_permissions || [];
      if (!currentPermissions.includes(permissionInput.trim())) {
        onChange({
          ...data,
          required_permissions: [...currentPermissions, permissionInput.trim()]
        });
      }
      setPermissionInput('');
    }
  };

  const removePermission = (permission: string) => {
    const currentPermissions = data.required_permissions || [];
    onChange({
      ...data,
      required_permissions: currentPermissions.filter(p => p !== permission)
    });
  };

  const addDataScope = () => {
    if (dataScopeInput.trim()) {
      const currentScopes = data.data_scopes || [];
      if (!currentScopes.includes(dataScopeInput.trim())) {
        onChange({
          ...data,
          data_scopes: [...currentScopes, dataScopeInput.trim()]
        });
      }
      setDataScopeInput('');
    }
  };

  const removeDataScope = (scope: string) => {
    const currentScopes = data.data_scopes || [];
    onChange({
      ...data,
      data_scopes: currentScopes.filter(s => s !== scope)
    });
  };

  return (
    <div className="space-y-6">
      {/* 权限模板选择 */}
      <div>
        <Label>权限模板</Label>
        <Select
          onValueChange={(templateId) => {
            const template = templates.find(t => t.id === templateId);
            if (template) {
              onChange({
                ...data,
                required_permissions: template.permissions,
                data_scopes: template.data_scopes,
                permission_mode: template.permission_mode
              });
            }
          }}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择权限模板（可选）" />
          </SelectTrigger>
          <SelectContent>
            {templates
              .filter(t => t.is_active)
              .map(template => (
                <SelectItem key={template.id} value={template.id}>
                  {template.template_name} ({template.template_type})
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      <Separator />

      {/* 权限配置 */}
      <div>
        <Label>所需权限</Label>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input
              value={permissionInput}
              onChange={(e) => setPermissionInput(e.target.value)}
              placeholder="输入权限，如：student:read"
              disabled={disabled}
              onKeyPress={(e) => e.key === 'Enter' && addPermission()}
            />
            <Button onClick={addPermission} disabled={disabled}>
              添加
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {(data.required_permissions || []).map((permission) => (
              <Badge key={permission} variant="secondary" className="flex items-center gap-1">
                {permission}
                {!disabled && (
                  <button
                    onClick={() => removePermission(permission)}
                    className="ml-1 text-red-500 hover:text-red-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 数据范围 */}
      <div>
        <Label>数据范围</Label>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input
              value={dataScopeInput}
              onChange={(e) => setDataScopeInput(e.target.value)}
              placeholder="输入数据范围，如：class:*"
              disabled={disabled}
              onKeyPress={(e) => e.key === 'Enter' && addDataScope()}
            />
            <Button onClick={addDataScope} disabled={disabled}>
              添加
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {(data.data_scopes || []).map((scope) => (
              <Badge key={scope} variant="outline" className="flex items-center gap-1">
                {scope}
                {!disabled && (
                  <button
                    onClick={() => removeDataScope(scope)}
                    className="ml-1 text-red-500 hover:text-red-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* 权限模式 */}
      <div>
        <Label>权限模式</Label>
        <Select
          value={data.permission_mode || 'any'}
          onValueChange={(value) => onChange({ ...data, permission_mode: value })}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">任一满足 (ANY)</SelectItem>
            <SelectItem value="all">全部满足 (ALL)</SelectItem>
            <SelectItem value="custom">自定义逻辑</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-500 mt-1">
          ANY: 满足任一权限即可访问 | ALL: 必须满足所有权限
        </p>
      </div>
    </div>
  );
};

// 高级设置表单
const AdvancedSettingsForm: React.FC<{
  data: Partial<MenuItem>;
  onChange: (data: Partial<MenuItem>) => void;
  disabled: boolean;
}> = ({ data, onChange, disabled }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="access_level">访问级别</Label>
          <Input
            id="access_level"
            type="number"
            min="0"
            max="100"
            value={data.access_level || 0}
            onChange={(e) => onChange({ ...data, access_level: parseInt(e.target.value) || 0 })}
            disabled={disabled}
          />
          <p className="text-xs text-gray-500 mt-1">0-100，用于排序和过滤</p>
        </div>
        <div>
          <Label htmlFor="sort_order">排序顺序</Label>
          <Input
            id="sort_order"
            type="number"
            value={data.sort_order || 0}
            onChange={(e) => onChange({ ...data, sort_order: parseInt(e.target.value) || 0 })}
            disabled={disabled}
          />
        </div>
      </div>

      <div>
        <Label htmlFor="component_path">前端组件路径</Label>
        <Input
          id="component_path"
          value={data.component_path || ''}
          onChange={(e) => onChange({ ...data, component_path: e.target.value })}
          disabled={disabled}
          placeholder="/path/to/Component"
        />
      </div>

      <div>
        <Label htmlFor="external_link">外部链接</Label>
        <Input
          id="external_link"
          value={data.external_link || ''}
          onChange={(e) => onChange({ ...data, external_link: e.target.value })}
          disabled={disabled}
          placeholder="https://example.com"
        />
      </div>

      <div>
        <Label>扩展元数据 (JSON)</Label>
        <Textarea
          value={data.metadata ? JSON.stringify(data.metadata, null, 2) : '{}'}
          onChange={(e) => {
            try {
              const metadata = JSON.parse(e.target.value);
              onChange({ ...data, metadata });
            } catch (error) {
              // 忽略无效JSON
            }
          }}
          disabled={disabled}
          rows={6}
          placeholder='{"key": "value"}'
        />
      </div>
    </div>
  );
};

// 使用统计组件
const UsageStatistics: React.FC<{
  stats: MenuItem['usage_stats'];
}> = ({ stats }) => {
  if (!stats) {
    return (
      <div className="text-center text-gray-500 py-8">
        暂无使用统计数据
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-blue-600">
            {stats.total_access_count}
          </div>
          <div className="text-sm text-gray-600">总访问次数</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-green-600">
            {stats.unique_user_count}
          </div>
          <div className="text-sm text-gray-600">独立用户数</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-red-600">
            {stats.denied_access_count}
          </div>
          <div className="text-sm text-gray-600">拒绝访问次数</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-purple-600">
            {stats.avg_daily_access.toFixed(1)}
          </div>
          <div className="text-sm text-gray-600">日均访问</div>
        </CardContent>
      </Card>
      
      {stats.last_accessed_at && (
        <Card className="col-span-2">
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">最后访问时间</div>
            <div className="text-lg font-medium">
              {new Date(stats.last_accessed_at).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SuperAdminMenuManagement;