import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  FileText,
  Search,
  Filter,
  Calendar,
  User,
  Settings,
  Eye,
  RotateCcw,
  Download,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// 类型定义
interface AuditLog {
  id: string;
  menu_id: string;
  operation: string;
  old_config?: any;
  new_config?: any;
  changes_summary: any;
  affected_users_count: number;
  operator_id: string;
  operator_identity: string;
  tenant_id?: string;
  reason?: string;
  impact_analysis: any;
  rollback_data?: any;
  is_rolled_back: boolean;
  rollback_at?: string;
  created_at: string;
  menu_name?: string;
  operator_name?: string;
  change_magnitude: string;
}

interface AuditStatistics {
  time_range: string;
  total_operations: number;
  operations_by_type: OperationTypeStats[];
  operations_by_operator: OperatorStats[];
  timeline: TimelinePoint[];
  risk_analysis: RiskAnalysis;
}

interface OperationTypeStats {
  operation: string;
  count: number;
  percentage: number;
  trend: string;
}

interface OperatorStats {
  operator_id: string;
  operator_identity: string;
  operator_name?: string;
  operation_count: number;
  most_common_operation: string;
  risk_score: number;
}

interface TimelinePoint {
  date: string;
  operation_count: number;
  unique_operators: number;
  high_risk_operations: number;
}

interface RiskAnalysis {
  overall_risk_level: string;
  risk_factors: RiskFactor[];
  recommendations: string[];
  anomalies_detected: number;
}

interface RiskFactor {
  factor_type: string;
  description: string;
  severity: string;
  affected_count: number;
}

const PermissionAuditLog: React.FC = () => {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [statistics, setStatistics] = useState<AuditStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  
  // 筛选条件
  const [filters, setFilters] = useState({
    operation: '',
    menu_id: '',
    operator_id: '',
    tenant_id: '',
    start_date: '',
    end_date: '',
    search: '',
    sort_by: 'created',
    sort_order: 'desc'
  });

  const { toast } = useToast();

  // 加载审计日志
  const loadAuditLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        page_size: pageSize.toString(),
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      });

      const response = await fetch(`/api/v1/admin/audit/logs?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAuditLogs(data.data || []);
        setTotalPages(Math.ceil(data.total / pageSize));
      } else {
        throw new Error('Failed to load audit logs');
      }
    } catch (error) {
      toast({
        title: "加载失败",
        description: "无法加载审计日志",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 加载统计数据
  const loadStatistics = async () => {
    try {
      const params = new URLSearchParams({
        time_range: 'month',
        group_by: 'operation'
      });

      const response = await fetch(`/api/v1/admin/audit/statistics?${params}`);
      if (response.ok) {
        const data = await response.json();
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  };

  useEffect(() => {
    loadAuditLogs();
  }, [currentPage, filters]);

  useEffect(() => {
    loadStatistics();
  }, []);

  // 应用筛选
  const applyFilters = (newFilters: typeof filters) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  // 重置筛选
  const resetFilters = () => {
    const resetFilters = {
      operation: '',
      menu_id: '',
      operator_id: '',
      tenant_id: '',
      start_date: '',
      end_date: '',
      search: '',
      sort_by: 'created',
      sort_order: 'desc'
    };
    setFilters(resetFilters);
    setCurrentPage(1);
  };

  // 导出审计日志
  const exportAuditLogs = async () => {
    try {
      const params = new URLSearchParams({
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      });

      const response = await fetch(`/api/v1/admin/audit/export?${params}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast({
          title: "导出成功",
          description: "审计日志已导出",
        });
      }
    } catch (error) {
      toast({
        title: "导出失败",
        description: "无法导出审计日志",
        variant: "destructive",
      });
    }
  };

  // 回滚变更
  const handleRollback = async (logId: string, reason: string) => {
    try {
      const response = await fetch('/api/v1/admin/audit/rollback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audit_log_id: logId,
          reason,
          confirm_rollback: true,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.data.rollback_successful) {
          toast({
            title: "回滚成功",
            description: "权限变更已回滚",
          });
          loadAuditLogs();
        } else {
          toast({
            title: "回滚失败",
            description: data.data.message,
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      toast({
        title: "回滚失败",
        description: "无法执行回滚操作",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">权限审计日志</h1>
          <p className="text-gray-600 mt-1">
            查看和管理所有权限变更的详细记录
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={exportAuditLogs} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
          <Button onClick={loadAuditLogs} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计概览 */}
      {statistics && (
        <StatisticsOverview statistics={statistics} />
      )}

      {/* 筛选器 */}
      <AuditFilters
        filters={filters}
        onApplyFilters={applyFilters}
        onResetFilters={resetFilters}
      />

      {/* 审计日志列表 */}
      <Card>
        <CardHeader>
          <CardTitle>审计记录</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : auditLogs.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <div className="text-gray-500">没有找到审计记录</div>
            </div>
          ) : (
            <div className="space-y-3">
              {auditLogs.map((log) => (
                <AuditLogItem
                  key={log.id}
                  log={log}
                  onViewDetails={() => setSelectedLog(log)}
                  onRollback={(reason) => handleRollback(log.id, reason)}
                />
              ))}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                第 {currentPage} 页，共 {totalPages} 页
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 详情对话框 */}
      {selectedLog && (
        <AuditLogDetails
          log={selectedLog}
          onClose={() => setSelectedLog(null)}
          onRollback={(reason) => handleRollback(selectedLog.id, reason)}
        />
      )}
    </div>
  );
};

// 统计概览组件
const StatisticsOverview: React.FC<{
  statistics: AuditStatistics;
}> = ({ statistics }) => {
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'decreasing': return <TrendingDown className="w-4 h-4 text-green-500" />;
      default: return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 总体统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">操作统计</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-blue-600 mb-2">
            {statistics.total_operations}
          </div>
          <div className="text-sm text-gray-600 mb-4">
            {statistics.time_range} 内的操作总数
          </div>
          
          <div className="space-y-2">
            {statistics.operations_by_type.slice(0, 3).map((op) => (
              <div key={op.operation} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm">{op.operation}</span>
                  {getTrendIcon(op.trend)}
                </div>
                <div className="text-sm font-medium">
                  {op.count} ({op.percentage.toFixed(1)}%)
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 操作员活跃度 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">活跃操作员</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {statistics.operations_by_operator.slice(0, 5).map((operator) => (
              <div key={operator.operator_id} className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">
                    {operator.operator_identity}
                  </div>
                  <div className="text-xs text-gray-500">
                    常用操作: {operator.most_common_operation}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {operator.operation_count}
                  </div>
                  <div className={`text-xs px-2 py-1 rounded ${
                    operator.risk_score > 0.7 ? 'bg-red-100 text-red-600' :
                    operator.risk_score > 0.4 ? 'bg-yellow-100 text-yellow-600' :
                    'bg-green-100 text-green-600'
                  }`}>
                    风险: {(operator.risk_score * 100).toFixed(0)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 风险分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">风险分析</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">整体风险等级</span>
              <Badge className={getRiskLevelColor(statistics.risk_analysis.overall_risk_level)}>
                {statistics.risk_analysis.overall_risk_level.toUpperCase()}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">异常检测</span>
              <div className="flex items-center space-x-1">
                <AlertTriangle className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium">
                  {statistics.risk_analysis.anomalies_detected}
                </span>
              </div>
            </div>

            <Separator />

            <div>
              <div className="text-sm text-gray-600 mb-2">主要风险因素</div>
              <div className="space-y-1">
                {statistics.risk_analysis.risk_factors.slice(0, 3).map((factor, index) => (
                  <div key={index} className="text-xs text-gray-500">
                    • {factor.description}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <div className="text-sm text-gray-600 mb-2">建议</div>
              <div className="space-y-1">
                {statistics.risk_analysis.recommendations.slice(0, 2).map((rec, index) => (
                  <div key={index} className="text-xs text-gray-500">
                    • {rec}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// 筛选器组件
const AuditFilters: React.FC<{
  filters: any;
  onApplyFilters: (filters: any) => void;
  onResetFilters: () => void;
}> = ({ filters, onApplyFilters, onResetFilters }) => {
  const [localFilters, setLocalFilters] = useState(filters);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleApply = () => {
    onApplyFilters(localFilters);
  };

  const handleReset = () => {
    onResetFilters();
    setLocalFilters({
      operation: '',
      menu_id: '',
      operator_id: '',
      tenant_id: '',
      start_date: '',
      end_date: '',
      search: '',
      sort_by: 'created',
      sort_order: 'desc'
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            筛选条件
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            {showAdvanced ? '简单筛选' : '高级筛选'}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 基础筛选 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <Label>搜索</Label>
            <Input
              placeholder="菜单ID、操作员..."
              value={localFilters.search}
              onChange={(e) => setLocalFilters({ ...localFilters, search: e.target.value })}
            />
          </div>
          <div>
            <Label>操作类型</Label>
            <Select value={localFilters.operation} onValueChange={(value) => setLocalFilters({ ...localFilters, operation: value })}>
              <SelectTrigger>
                <SelectValue placeholder="全部操作" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部操作</SelectItem>
                <SelectItem value="CREATE">创建</SelectItem>
                <SelectItem value="UPDATE">更新</SelectItem>
                <SelectItem value="DELETE">删除</SelectItem>
                <SelectItem value="BATCH_UPDATE">批量更新</SelectItem>
                <SelectItem value="ROLLBACK">回滚</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>排序方式</Label>
            <Select value={localFilters.sort_by} onValueChange={(value) => setLocalFilters({ ...localFilters, sort_by: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">创建时间</SelectItem>
                <SelectItem value="operation">操作类型</SelectItem>
                <SelectItem value="operator">操作员</SelectItem>
                <SelectItem value="impact">影响程度</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>排序顺序</Label>
            <Select value={localFilters.sort_order} onValueChange={(value) => setLocalFilters({ ...localFilters, sort_order: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">降序</SelectItem>
                <SelectItem value="asc">升序</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 高级筛选 */}
        {showAdvanced && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
            <div>
              <Label>菜单ID</Label>
              <Input
                placeholder="特定菜单ID"
                value={localFilters.menu_id}
                onChange={(e) => setLocalFilters({ ...localFilters, menu_id: e.target.value })}
              />
            </div>
            <div>
              <Label>操作员ID</Label>
              <Input
                placeholder="特定操作员ID"
                value={localFilters.operator_id}
                onChange={(e) => setLocalFilters({ ...localFilters, operator_id: e.target.value })}
              />
            </div>
            <div>
              <Label>租户ID</Label>
              <Input
                placeholder="特定租户ID"
                value={localFilters.tenant_id}
                onChange={(e) => setLocalFilters({ ...localFilters, tenant_id: e.target.value })}
              />
            </div>
            <div>
              <Label>开始日期</Label>
              <Input
                type="date"
                value={localFilters.start_date}
                onChange={(e) => setLocalFilters({ ...localFilters, start_date: e.target.value })}
              />
            </div>
            <div>
              <Label>结束日期</Label>
              <Input
                type="date"
                value={localFilters.end_date}
                onChange={(e) => setLocalFilters({ ...localFilters, end_date: e.target.value })}
              />
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
          <Button onClick={handleApply}>
            <Search className="w-4 h-4 mr-2" />
            应用筛选
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// 审计日志项组件
const AuditLogItem: React.FC<{
  log: AuditLog;
  onViewDetails: () => void;
  onRollback: (reason: string) => void;
}> = ({ log, onViewDetails, onRollback }) => {
  const [showRollbackDialog, setShowRollbackDialog] = useState(false);
  const [rollbackReason, setRollbackReason] = useState('');

  const getOperationColor = (operation: string) => {
    switch (operation) {
      case 'CREATE': return 'bg-green-100 text-green-800';
      case 'UPDATE': return 'bg-blue-100 text-blue-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      case 'ROLLBACK': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMagnitudeColor = (magnitude: string) => {
    switch (magnitude) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'major': return 'bg-orange-100 text-orange-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'minor': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleRollback = () => {
    if (rollbackReason.trim()) {
      onRollback(rollbackReason);
      setShowRollbackDialog(false);
      setRollbackReason('');
    }
  };

  return (
    <div className="border rounded-lg p-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <Badge className={getOperationColor(log.operation)}>
              {log.operation}
            </Badge>
            <Badge className={getMagnitudeColor(log.change_magnitude)}>
              {log.change_magnitude}
            </Badge>
            {log.is_rolled_back && (
              <Badge variant="outline">
                已回滚
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">菜单: </span>
              <span className="font-medium">
                {log.menu_name || log.menu_id}
              </span>
            </div>
            <div>
              <span className="text-gray-600">操作员: </span>
              <span className="font-medium">
                {log.operator_identity}
              </span>
            </div>
            <div>
              <span className="text-gray-600">时间: </span>
              <span className="font-medium">
                {new Date(log.created_at).toLocaleString()}
              </span>
            </div>
          </div>

          {log.reason && (
            <div className="mt-2 text-sm text-gray-600">
              <span className="font-medium">原因: </span>
              {log.reason}
            </div>
          )}

          {log.affected_users_count > 0 && (
            <div className="mt-2 flex items-center text-sm text-orange-600">
              <User className="w-4 h-4 mr-1" />
              影响 {log.affected_users_count} 个用户
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onViewDetails}
          >
            <Eye className="w-4 h-4 mr-1" />
            详情
          </Button>
          {!log.is_rolled_back && log.rollback_data && (
            <Dialog open={showRollbackDialog} onOpenChange={setShowRollbackDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <RotateCcw className="w-4 h-4 mr-1" />
                  回滚
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>确认回滚操作</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    此操作将回滚菜单 <strong>{log.menu_name || log.menu_id}</strong> 的权限配置变更。
                  </div>
                  <div>
                    <Label>回滚原因 *</Label>
                    <Input
                      value={rollbackReason}
                      onChange={(e) => setRollbackReason(e.target.value)}
                      placeholder="请输入回滚原因"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowRollbackDialog(false)}
                    >
                      取消
                    </Button>
                    <Button
                      onClick={handleRollback}
                      disabled={!rollbackReason.trim()}
                    >
                      确认回滚
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>
    </div>
  );
};

// 审计日志详情组件
const AuditLogDetails: React.FC<{
  log: AuditLog;
  onClose: () => void;
  onRollback: (reason: string) => void;
}> = ({ log, onClose, onRollback }) => {
  const [rollbackReason, setRollbackReason] = useState('');
  const [showRollbackForm, setShowRollbackForm] = useState(false);

  const handleRollback = () => {
    if (rollbackReason.trim()) {
      onRollback(rollbackReason);
      onClose();
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>审计日志详情</DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-6">
            {/* 基本信息 */}
            <div>
              <h3 className="text-lg font-medium mb-3">基本信息</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">日志ID: </span>
                  <span className="font-mono">{log.id}</span>
                </div>
                <div>
                  <span className="text-gray-600">操作类型: </span>
                  <Badge className={`ml-2 ${
                    log.operation === 'CREATE' ? 'bg-green-100 text-green-800' :
                    log.operation === 'UPDATE' ? 'bg-blue-100 text-blue-800' :
                    log.operation === 'DELETE' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {log.operation}
                  </Badge>
                </div>
                <div>
                  <span className="text-gray-600">菜单ID: </span>
                  <span className="font-medium">{log.menu_id}</span>
                </div>
                <div>
                  <span className="text-gray-600">菜单名称: </span>
                  <span className="font-medium">{log.menu_name || '未知'}</span>
                </div>
                <div>
                  <span className="text-gray-600">操作员: </span>
                  <span className="font-medium">{log.operator_identity}</span>
                </div>
                <div>
                  <span className="text-gray-600">租户ID: </span>
                  <span className="font-medium">{log.tenant_id || '系统级'}</span>
                </div>
                <div>
                  <span className="text-gray-600">操作时间: </span>
                  <span className="font-medium">{new Date(log.created_at).toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-gray-600">影响程度: </span>
                  <Badge className={`ml-2 ${
                    log.change_magnitude === 'critical' ? 'bg-red-100 text-red-800' :
                    log.change_magnitude === 'major' ? 'bg-orange-100 text-orange-800' :
                    log.change_magnitude === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {log.change_magnitude}
                  </Badge>
                </div>
              </div>
            </div>

            <Separator />

            {/* 变更摘要 */}
            <div>
              <h3 className="text-lg font-medium mb-3">变更摘要</h3>
              <div className="bg-gray-50 rounded p-4">
                <pre className="text-sm whitespace-pre-wrap">
                  {JSON.stringify(log.changes_summary, null, 2)}
                </pre>
              </div>
            </div>

            {/* 配置对比 */}
            {log.old_config && log.new_config && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-3">配置对比</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2 text-red-600">变更前</h4>
                      <div className="bg-red-50 rounded p-4 max-h-64 overflow-auto">
                        <pre className="text-xs whitespace-pre-wrap">
                          {JSON.stringify(log.old_config, null, 2)}
                        </pre>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2 text-green-600">变更后</h4>
                      <div className="bg-green-50 rounded p-4 max-h-64 overflow-auto">
                        <pre className="text-xs whitespace-pre-wrap">
                          {JSON.stringify(log.new_config, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* 影响分析 */}
            {log.impact_analysis && Object.keys(log.impact_analysis).length > 0 && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-3">影响分析</h3>
                  <div className="bg-blue-50 rounded p-4">
                    <pre className="text-sm whitespace-pre-wrap">
                      {JSON.stringify(log.impact_analysis, null, 2)}
                    </pre>
                  </div>
                </div>
              </>
            )}

            {/* 回滚信息 */}
            {log.is_rolled_back && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-3">回滚信息</h3>
                  <div className="bg-purple-50 rounded p-4">
                    <div className="text-sm">
                      <div className="mb-2">
                        <span className="text-gray-600">回滚时间: </span>
                        <span className="font-medium">
                          {log.rollback_at ? new Date(log.rollback_at).toLocaleString() : '未知'}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                        <span className="text-green-600 font-medium">
                          此变更已被回滚
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* 回滚操作 */}
            {!log.is_rolled_back && log.rollback_data && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-3">回滚操作</h3>
                  {showRollbackForm ? (
                    <div className="space-y-4">
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
                        <div className="flex items-center mb-2">
                          <AlertTriangle className="w-4 h-4 text-yellow-600 mr-2" />
                          <span className="text-yellow-800 font-medium">
                            警告：回滚操作不可撤销
                          </span>
                        </div>
                        <div className="text-sm text-yellow-700">
                          此操作将恢复菜单的权限配置到变更前的状态，请确认您了解此操作的影响。
                        </div>
                      </div>
                      <div>
                        <Label>回滚原因 *</Label>
                        <Input
                          value={rollbackReason}
                          onChange={(e) => setRollbackReason(e.target.value)}
                          placeholder="请输入回滚原因"
                        />
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => setShowRollbackForm(false)}
                        >
                          取消
                        </Button>
                        <Button
                          onClick={handleRollback}
                          disabled={!rollbackReason.trim()}
                        >
                          <RotateCcw className="w-4 h-4 mr-2" />
                          确认回滚
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={() => setShowRollbackForm(true)}
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      执行回滚
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default PermissionAuditLog;