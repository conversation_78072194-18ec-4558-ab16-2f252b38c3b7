# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Deep-Mate (深度学伴) is a comprehensive educational platform that provides efficient exam and homework management solutions for schools. The system features multi-tenant architecture with dynamic schema creation, supporting complex educational workflows including exam management, automated grading, academic analytics, and cross-tenant collaboration.

## Architecture

### Full-Stack Architecture
- **Backend**: Rust + Axum web framework + PostgreSQL
- **Frontend**: React + TypeScript + Vite + shadcn/ui
- **Database**: PostgreSQL 18 with multi-schema tenant isolation
- **Authentication**: JWT-based with role-based access control (RBAC)
- **File Storage**: MinIO distributed object storage (S3-compatible)
- **Real-time**: WebSocket for live updates

### Multi-Tenant Design
- **Schema Isolation**: Each tenant gets a dedicated PostgreSQL schema (`tenant_001`, `tenant_002`, etc.)
- **Public Schema**: Shared resources (users, roles, question banks, textbooks)
- **Dynamic Creation**: Tenant schemas are created automatically when new schools are added
- **Cross-Tenant Features**: Support for multi-school joint exams and parent accounts

### Key Business Domains
- **User Management**: Phone-based registration with identity binding
- **Tenant Management**: School organizations with dynamic schema creation
- **Exam Management**: Single-school and joint-school examinations
- **Grading System**: Manual, AI-assisted, and hybrid grading workflows
- **Academic Analytics**: Performance tracking and statistical analysis
- **Permission System**: Complex role-based access with special authorization
- **Class Management**: Dual-storage class-teacher assignment system with automatic sync

## Development Commands

### Backend (Rust)
```bash
# Development
cd backend
cargo run                    # Start development server (http://localhost:8080)
cargo build                 # Build project
cargo test                  # Run all tests
cargo test <test_name>       # Run specific test
cargo test -- --nocapture   # Run tests with output
cargo check                 # Quick syntax check
cargo clippy                # Lint code
cargo clean                 # Clean build artifacts

# Database Operations
sqlx migrate run            # Run database migrations
sqlx migrate add <name>     # Create new migration
sqlx migrate revert         # Revert last migration

# Database Setup
createdb deep_mate          # Create database
./scripts/test_admin_api.sh [BASE_URL] [ADMIN_TOKEN]  # Test admin APIs
./scripts/test_login.sh     # Test login functionality 
./scripts/reset_migrations.sh # Reset migrations (development only)
./scripts/fix_migrations.sh   # Fix migration issues

# Testing Individual Components
cargo test auth_tests        # Test authentication module
cargo test -- --test-threads=1  # Run tests sequentially
```

### Frontend (React/TypeScript)
```bash
# Development
cd frontend
npm run dev                 # Start dev server (http://localhost:5173)
npm run build              # Build for production
npm run preview            # Preview production build

# Code Quality
npm run lint               # Run ESLint
npm run typecheck          # TypeScript type checking
npm test                   # Run tests (if configured)

# Dependencies
npm install                # Install dependencies
npm ci                     # Clean install for CI/CD
```

### Full Stack Development
```bash
# Start both servers simultaneously (in separate terminals)
# Terminal 1:
cd backend && cargo run

# Terminal 2:
cd frontend && npm run dev
```

## Project Structure

### Backend Structure (`/backend/src/`)
```
├── controller/           # HTTP request handlers
│   ├── auth/            # Authentication endpoints
│   ├── tenant/          # Tenant management
│   ├── user/            # User management
│   ├── classes/         # Class management
│   ├── exam/            # Exam management
│   ├── grading/         # Grading system
│   └── analysis/        # Analytics endpoints
├── service/             # Business logic layer
├── model/               # Data structures and database models
├── middleware/          # HTTP middleware (auth, tenant context)
├── utils/               # Utility functions (JWT, password, DB helpers)
├── config/              # Configuration management
└── tests/               # Unit and integration tests
```

### Frontend Structure (`/frontend/src/`)
```
├── components/ui/       # Reusable UI components (shadcn/ui)
├── pages/               # Page components organized by feature
│   ├── AcademicAnalysis/    # Academic analysis pages
│   ├── Class/               # Class management pages
│   ├── ExamManagement/      # Exam management pages
│   ├── GradingCenter/       # Grading system pages
│   ├── Homework/            # Homework management pages
│   ├── QuestionManagement/  # Question bank and textbook pages
│   ├── RoleManagement/      # Role and permission pages
│   ├── Statistics/          # Statistical analysis pages
│   ├── TeachingAids/        # Teaching aids pages
│   └── Tenant/              # Tenant management pages
├── services/            # API client services
├── types/               # TypeScript type definitions
├── contexts/            # React context providers (auth, theme)
├── hooks/               # Custom React hooks (use-mobile, etc.)
├── router/              # React Router configuration and protected routes
├── layouts/             # Layout components (RootLayout)
└── lib/                 # Utility functions
```

### Database Architecture
- **Public Schema**: Global shared tables (users, tenants, roles, question_bank, textbooks)
- **Tenant Schemas**: Business data isolation (classes, exams, students, grading_records)
- **Migration Strategy**: Sequential migrations with tenant template system

## Environment Setup

### Required Environment Variables

#### Backend (`.env`)
```env
DATABASE_URL=postgresql://username:password@localhost/deep_mate
JWT_SECRET=your-super-secret-jwt-key
HOST=127.0.0.1
PORT=8080
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
```

#### Frontend (`.env`)
```env
VITE_API_BASE_URL=http://localhost:8080
VITE_NODE_ENV=development
```

## Key Features and Workflows

### Authentication System
- **Registration**: Phone number + SMS verification
- **Login**: Phone/username + password or SMS code
- **Identity Binding**: Users self-bind to school identities using personal info
- **Multi-Identity**: Users can have multiple identities across tenants
- **Identity Switching**: Seamless switching between roles/tenants

### Permission System
- **Role Hierarchy**: 校长 → 教导主任 → 学科组长/年级长 → 班主任/任课老师 → 学生
- **Dynamic Calculation**: Real-time permission calculation based on roles + organizational structure
- **Special Authorization**: Temporary permissions with approval workflow
- **Audit Logging**: Complete permission access tracking

### Multi-Tenant Features
- **Tenant Creation**: Admin API for creating new school tenants
- **Schema Management**: Automatic schema creation with template system
- **Cross-Tenant Operations**: Joint exams and parent-student relationships
- **Tenant Isolation**: Complete data separation between schools

## API Architecture

### REST API Standards
- **Base URL**: `http://localhost:8080/api/v1/`
- **Authentication**: Bearer token (JWT)
- **Response Format**: Standardized JSON with `success`, `code`, `message`, `data`, `meta`
- **Error Handling**: HTTP status codes with detailed error messages
- **Versioning**: URL path versioning (`/api/v1/`, `/api/v2/`)

### Key API Endpoints
```
# Authentication
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout

# Identity Management
POST /api/v1/identity/bind
POST /api/v1/identity/unbind
POST /api/v1/users/{id}/switch-identity

# Tenant Management (Admin only)
GET  /api/admin/tenants
POST /api/admin/tenants
PUT  /api/admin/tenants/{id}

# Business APIs
GET  /api/v1/classes
POST /api/v1/exams
GET  /api/v1/grading/progress
GET  /api/v1/analytics/scores
```

## Database Best Practices

### Migration Management
- Use `sqlx migrate add` to create new migrations
- Always test migrations on development data first
- Include both UP and DOWN migrations
- Use descriptive migration names with timestamps

### Multi-Schema Considerations
- **Public Schema**: Global tables accessible across tenants
- **Tenant Schema**: Business data specific to each school
- **Schema Naming**: `tenant_{tenant_code}_{id}` format
- **Cross-Schema Queries**: Use fully qualified table names

### Performance Optimization
- Index frequently queried fields (user_id, tenant_id, exam_id)
- Use connection pooling (SQLx PgPool)
- Implement proper foreign key constraints
- Consider read replicas for analytics queries

## Development Workflow

### Before Starting Development
1. Ensure PostgreSQL 18 is running
2. Run database migrations: `sqlx migrate run`
3. Verify environment variables are set
4. Start backend: `cargo run`
5. Start frontend: `npm run dev`

### Making Changes
1. **Backend Changes**: Modify Rust code, Cargo will auto-recompile
2. **Frontend Changes**: Vite provides hot module replacement
3. **Database Changes**: Create migration files, test thoroughly
4. **API Changes**: Update both backend endpoints and frontend services

### Testing Strategy
- **Unit Tests**: `cargo test` for business logic
- **Integration Tests**: Test full API endpoints
- **Frontend Tests**: Component and service testing
- **Manual Testing**: Use provided shell scripts for API testing

### Testing Scripts Usage
```bash
# Backend API Testing
cd backend

# Test admin tenant APIs (requires admin JWT token)
./scripts/test_admin_api.sh http://localhost:8080 YOUR_ADMIN_TOKEN

# Test authentication endpoints
./scripts/test_login.sh

# Reset migrations in development (USE WITH CAUTION)
./scripts/reset_migrations.sh

# Fix migration issues
./scripts/fix_migrations.sh
```

## Code Style and Standards

### Rust Backend
- Use `rustfmt` for code formatting
- Follow Rust naming conventions (snake_case, CamelCase)
- Use `anyhow` for error handling in services
- Implement proper logging with `tracing`
- Use `async/await` for all I/O operations

### TypeScript Frontend
- Use ES6+ features and TypeScript strict mode
- Follow React functional component patterns with hooks
- Use shadcn/ui components for consistency
- Implement proper error boundaries
- Use React Query or similar for API state management

### Database Schema
- Use descriptive table and column names
- Include appropriate constraints and indexes
- Document complex business rules in comments
- Follow the established multi-tenant pattern

## Security Considerations

### Authentication & Authorization
- JWT tokens with reasonable expiration times
- Role-based access control with principle of least privilege
- Special permission requests require approval workflow
- Complete audit logging of all access attempts

### Data Protection
- Tenant data isolation through schema separation
- Input validation and sanitization
- SQL injection prevention through prepared statements
- Secure file upload handling with MinIO

### API Security
- Rate limiting on authentication endpoints
- CORS configuration for frontend access
- Request validation using strong typing
- Sensitive data exclusion from logs

## Troubleshooting

### Common Backend Issues
- **Database Connection**: Check DATABASE_URL and PostgreSQL service
- **Migration Failures**: Use `./scripts/reset_migrations.sh` in development
- **JWT Issues**: Verify JWT_SECRET is set and consistent
- **Compilation Errors**: Run `cargo clean` and rebuild

### Common Frontend Issues
- **API Connection**: Verify VITE_API_BASE_URL points to running backend
- **Build Errors**: Clear node_modules and reinstall dependencies
- **Type Errors**: Run `npm run typecheck` to identify issues
- **Hot Reload Issues**: Restart dev server

### Database Issues
- **Schema Creation**: Check tenant creation logs and permissions
- **Migration Conflicts**: Reset development database if needed
- **Performance Issues**: Check query plans and add indexes
- **Connection Pool**: Monitor connection usage in logs

## Additional Resources

- **Architecture Documentation**: `/docs/PRD_modules/3_core_architecture.md`
- **API Documentation**: `/backend/docs/admin_tenant_api.md`
- **Developer Setup**: `/docs/implementation-guides/developer-setup/README.md`
- **Dynamic Tenant Guide**: `/backend/README_DYNAMIC_TENANT.md`
- **Teacher Import System**: `/backend/docs/TEACHER_IMPORT_GUIDE.md`
- **Class Teacher Assignment System**: `/backend/docs/CLASS_TEACHER_DUAL_STORAGE_DESIGN.md`

## Key Architecture Decisions

### Class-Teacher Assignment System
The system uses a **dual-storage + master-slave sync** approach for managing class-teacher relationships, with **delayed permission creation** to handle real-world scenarios:

- **Primary Data Source**: `classes.teacher_id` - Direct business relationship, can be set even when teacher has no user account
- **Permission Data Source**: `user_identities` - Role-based access control, created only when teacher binds to user account
- **Conditional Sync**: Database triggers create permission records only when `teachers.user_id` is not NULL
- **Delayed Permission Creation**: When teachers bind to user accounts, system automatically creates permissions for all their assigned classes
- **Consistency Monitoring**: Built-in tools that understand the delayed sync pattern and only flag true inconsistencies

**Design Rationale**: Educational institutions often assign teachers to classes before those teachers create user accounts (e.g., during semester planning, data imports from HR systems). The system prioritizes business operations while ensuring permissions are correctly created when teachers join the platform.

**Real-world Flow**:
1. **Pre-semester**: Admin assigns teachers to classes (business data created)
2. **Teacher registration**: Teachers create accounts and bind to their identities
3. **Automatic sync**: System creates permission records for all previously assigned classes
4. **Permission activation**: Teachers can now access their class management features

**Key Files**:
- Migration: `/backend/migrations/20250731_class_teacher_role_sync_system.sql`
- Service: `/backend/src/service/class_teacher_assignment_service.rs`
- Identity Sync: `/backend/src/service/teacher_class_identity_sync.rs`
- Controller: `/backend/src/controller/class_teacher_assignment_controller.rs`

### Teacher Import and Identity Binding
The system supports importing teacher data before user accounts exist through a **pre-import + flexible binding** mechanism:

- **Pre-Import Support**: Teachers can be imported with `user_id=NULL` and binding status tracking
- **Smart Identity Discovery**: Automatic matching during user registration based on phone/email/ID
- **Flexible Binding**: High-confidence auto-binding + manual verification for complex cases
- **Audit Trail**: Complete logging of all binding operations for security and compliance

**Key Files**:
- Migration: `/backend/migrations/20250731_teacher_import_enhancement.sql`
- Service: `/backend/src/service/teacher_import_service.rs`
- Enhanced Auth: `/backend/src/service/auth/enhanced_auth_service.rs`