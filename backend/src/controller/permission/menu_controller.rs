use std::sync::Arc;
use axum::{
    extract::{State, Query, Path},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
    Extension,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error, debug};

use crate::service::permission::{
    CasbinPermissionService,
    MultiTenantCasbinService,
    MenuPermission,
};
use crate::middleware::auth_middleware::{AuthExtractor};
use crate::middleware::permission_middleware::{UserContextExtractor};
use crate::utils::api_response::ApiResponse;

/// 菜单查询参数
#[derive(Debug, Deserialize)]
pub struct MenuQueryParams {
    pub tenant_id: Option<String>,
    pub menu_type: Option<String>,  // 菜单类型过滤: main, admin, personal
}

/// 菜单权限响应
#[derive(Debug, Serialize)]
pub struct MenuPermissionResponse {
    pub menus: Vec<MenuPermission>,
    pub total_count: i32,
    pub filtered_count: i32,
    pub user_identity: String,
    pub tenant_id: String,
}

/// 菜单可访问性检查请求
#[derive(Debug, Deserialize)]
pub struct MenuAccessCheckRequest {
    pub menu_ids: Vec<String>,
    pub tenant_id: String,
}

/// 菜单可访问性检查响应
#[derive(Debug, Serialize)]
pub struct MenuAccessCheckResponse {
    pub menu_id: String,
    pub accessible: bool,
    pub reason: Option<String>,
    pub required_permissions: Vec<String>,
    pub user_permissions: Vec<String>,
}

/// 批量菜单访问检查响应
#[derive(Debug, Serialize)]
pub struct BatchMenuAccessResponse {
    pub results: Vec<MenuAccessCheckResponse>,
    pub summary: MenuAccessSummary,
}

/// 菜单访问摘要
#[derive(Debug, Serialize)]
pub struct MenuAccessSummary {
    pub total_checked: i32,
    pub accessible_count: i32,
    pub denied_count: i32,
    pub check_duration_ms: u64,
}

/// 用户角色信息
#[derive(Debug, Serialize)]
pub struct UserRole {
    pub role_id: String,
    pub role_code: String,
    pub role_name: String,
    pub level: i32,
    pub category: String,
}

/// 数据权限范围响应
#[derive(Debug, Serialize)]
pub struct DataScopesResponse {
    pub data_scopes: Vec<DataScope>,
    pub resource: String,
    pub user_identity: String,
    pub tenant_id: String,
}

/// 数据权限范围
#[derive(Debug, Serialize)]
pub struct DataScope {
    pub resource: String,
    pub scope_type: String,
    pub scope_value: String,
    pub actions: Vec<String>,
}

/// 用户角色响应
#[derive(Debug, Serialize)]
pub struct UserRolesResponse {
    pub roles: Vec<UserRole>,
    pub user_identity: String,
    pub tenant_id: String,
}

/// 数据权限范围查询参数
#[derive(Debug, Deserialize)]
pub struct DataScopeQueryParams {
    pub tenant_id: Option<String>,
    pub resource: String,
}

/// 菜单权限控制器
pub struct MenuPermissionController;

impl MenuPermissionController {
    /// 获取用户可访问的菜单列表
    pub async fn get_user_menus(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<MenuQueryParams>,
    ) -> Result<Json<ApiResponse<MenuPermissionResponse>>, StatusCode> {
        let start_time = std::time::Instant::now();
        
        // 确定租户ID - 从查询参数或默认使用第一个租户
        let tenant_id = if let Some(tid) = params.tenant_id {
            tid
        } else {
            // 如果没有指定租户ID，使用用户的第一个租户
            if let Some(first_role) = auth_context.roles.first() {
                first_role.tenant_id.map(|id| id.to_string()).unwrap_or_else(|| "default".to_string())
            } else {
                "default".to_string()
            }
        };
        
        debug!("Getting menus for user {} in tenant {} (is_system_admin: {})", 
               auth_context.user_id, tenant_id, auth_context.is_super_admin());
        
        // 获取用户可访问的菜单
        let user_menus = if auth_context.is_super_admin() {
            // 如果是系统管理员，返回所有菜单
            debug!("User is system admin, returning all menus");
            casbin_service
                .get_all_menus()
                .await
                .map_err(|e| {
                    error!("Failed to get all menus for admin: {}", e);
                    StatusCode::INTERNAL_SERVER_ERROR
                })?
        } else {
            // 普通用户，根据权限过滤菜单
            // 构建用户身份标识 (修复：使用正确的user:id格式)
            let user_identity = format!("user:{}", auth_context.user_id);
            
            casbin_service
                .get_user_menus(&user_identity, &tenant_id)
                .await
                .map_err(|e| {
                    error!("Failed to get user menus: {}", e);
                    StatusCode::INTERNAL_SERVER_ERROR
                })?
        };
        
        // 根据菜单类型进行过滤
        let filtered_menus = if let Some(menu_type) = &params.menu_type {
            Self::filter_menus_by_type(user_menus, menu_type)
        } else {
            user_menus
        };
        
        let total_count = filtered_menus.len() as i32;
        let filtered_count = total_count; // 当前等于总数，未来可扩展更多过滤逻辑
        
        let duration = start_time.elapsed();
        info!("Retrieved {} menus for user {} (admin: {}) in {}ms", 
              total_count, auth_context.user_id, auth_context.is_super_admin(), duration.as_millis());
        
        let response = MenuPermissionResponse {
            menus: filtered_menus,
            total_count,
            filtered_count,
            user_identity: auth_context.user_id.to_string(),
            tenant_id: tenant_id.clone(),
        };
        
        Ok(Json(ApiResponse::success(response,Some("获取菜单成功".to_string()))))
    }
    
    /// 检查单个菜单的访问权限
    pub async fn check_menu_access(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path((tenant_id, menu_id)): Path<(String, String)>,
    ) -> Result<Json<ApiResponse<MenuAccessCheckResponse>>, StatusCode> {
        debug!("Checking menu access for user {} -> menu {} in tenant {}", 
               auth_context.user_id, menu_id, tenant_id);
        
        // 构建用户身份标识 (修复：使用正确的user:id格式)
        let user_identity = format!("user:{}", auth_context.user_id);
        
        // 获取菜单的权限要求
        let menu_permissions = casbin_service
            .get_user_menus(&user_identity, &tenant_id)
            .await
            .map_err(|e| {
                error!("Failed to get menu permissions: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;
        
        // 查找指定菜单
        let target_menu = Self::find_menu_by_id(&menu_permissions, &menu_id);
        
        let result = match target_menu {
            Some(menu) => {
                // 检查用户是否有访问该菜单的权限
                let has_access = Self::check_menu_permissions(
                    &casbin_service,
                    &auth_context,
                    &tenant_id,
                    menu,
                ).await?;
                
                // 构建角色列表
                let user_roles: Vec<String> = auth_context.roles
                    .iter()
                    .map(|role| role.identity_type.clone())
                    .collect();
                
                MenuAccessCheckResponse {
                    menu_id: menu_id.clone(),
                    accessible: has_access,
                    reason: if has_access { None } else { 
                        Some("Insufficient permissions".to_string()) 
                    },
                    required_permissions: menu.required_permissions.clone(),
                    user_permissions: user_roles,
                }
            }
            None => {
                // 构建角色列表
                let user_roles: Vec<String> = auth_context.roles
                    .iter()
                    .map(|role| role.identity_type.clone())
                    .collect();
                
                MenuAccessCheckResponse {
                    menu_id: menu_id.clone(),
                    accessible: false,
                    reason: Some("Menu not found".to_string()),
                    required_permissions: vec![],
                    user_permissions: user_roles,
                }
            }
        };
        
        Ok(Json(ApiResponse::success(result, Some("检查菜单访问权限成功".to_string()))))
    }
    
    /// 批量检查菜单访问权限
    pub async fn batch_check_menu_access(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<MenuAccessCheckRequest>,
    ) -> Result<Json<ApiResponse<BatchMenuAccessResponse>>, StatusCode> {
        let start_time = std::time::Instant::now();
        
        debug!("Batch checking menu access for user {} -> {} menus in tenant {}", 
               auth_context.user_id, request.menu_ids.len(), request.tenant_id);
        
        // 构建用户身份标识 (修复：使用正确的user:id格式)
        let user_identity = format!("user:{}", auth_context.user_id);
        
        // 获取所有菜单权限
        let all_menus = casbin_service
            .get_user_menus(&user_identity, &request.tenant_id)
            .await
            .map_err(|e| {
                error!("Failed to get menu permissions: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;
        
        let mut results = Vec::new();
        let mut accessible_count = 0;
        
        // 逐个检查菜单权限
        for menu_id in &request.menu_ids {
            let target_menu = Self::find_menu_by_id(&all_menus, menu_id);
            
            let result = match target_menu {
                Some(menu) => {
                    let has_access = Self::check_menu_permissions(
                        &casbin_service,
                        &auth_context,
                        &request.tenant_id,
                        menu,
                    ).await?;
                    
                    if has_access {
                        accessible_count += 1;
                    }
                    
                    // 构建角色列表
                    let user_roles: Vec<String> = auth_context.roles
                        .iter()
                        .map(|role| role.identity_type.clone())
                        .collect();
                    
                    MenuAccessCheckResponse {
                        menu_id: menu_id.clone(),
                        accessible: has_access,
                        reason: if has_access { None } else { 
                            Some("Insufficient permissions".to_string()) 
                        },
                        required_permissions: menu.required_permissions.clone(),
                        user_permissions: user_roles,
                    }
                }
                None => {
                    // 构建角色列表
                    let user_roles: Vec<String> = auth_context.roles
                        .iter()
                        .map(|role| role.identity_type.clone())
                        .collect();
                    
                    MenuAccessCheckResponse {
                        menu_id: menu_id.clone(),
                        accessible: false,
                        reason: Some("Menu not found".to_string()),
                        required_permissions: vec![],
                        user_permissions: user_roles,
                    }
                }
            };
            
            results.push(result);
        }
        
        let duration = start_time.elapsed();
        let total_checked = request.menu_ids.len() as i32;
        let denied_count = total_checked - accessible_count;
        
        let summary = MenuAccessSummary {
            total_checked,
            accessible_count,
            denied_count,
            check_duration_ms: duration.as_millis() as u64,
        };
        
        info!("Batch menu check completed: {}/{} accessible in {}ms", 
              accessible_count, total_checked, duration.as_millis());
        
        let response = BatchMenuAccessResponse {
            results,
            summary,
        };
        
        Ok(Json(ApiResponse::success(response, Some("批量检查菜单访问权限成功".to_string()))))
    }
    /// 获取用户角色信息
    pub async fn get_user_roles(
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<MenuQueryParams>,
    ) -> Result<Json<ApiResponse<UserRolesResponse>>, StatusCode> {
        let start_time = std::time::Instant::now();
        
        // 确定租户ID - 从查询参数或默认使用第一个租户
        let tenant_id = if let Some(tid) = params.tenant_id {
            tid
        } else {
            // 如果没有指定租户ID，使用用户的第一个租户
            if let Some(first_role) = auth_context.roles.first() {
                first_role.tenant_id.map(|id| id.to_string()).unwrap_or_else(|| "default".to_string())
            } else {
                "default".to_string()
            }
        };
            
        debug!("Getting roles for user {} in tenant {} (is_admin: {})", 
               auth_context.user_id, tenant_id, auth_context.is_super_admin());
        
        // 构建角色响应数据
        let mut roles = Vec::new();
        
        // 从认证上下文中提取角色信息
        for (index, auth_role) in auth_context.roles.iter().enumerate() {
            let role = UserRole {
                role_id: format!("role_{}", index),
                role_code: auth_role.identity_type.clone(),
                role_name: Self::get_role_display_name(&auth_role.identity_type),
                level: Self::get_role_level(&auth_role.identity_type),
                category: Self::get_role_category(&auth_role.identity_type),
            };
            roles.push(role);
        }
        
        // 如果是系统管理员，确保包含管理员角色
        if auth_context.is_super_admin() && !auth_context.roles.iter().any(|r| r.identity_type.contains("admin")) {
            roles.push(UserRole {
                role_id: "role_system_admin".to_string(),
                role_code: "super_admin".to_string(),
                role_name: "系统管理员".to_string(),
                level: 1,
                category: "system".to_string(),
            });
        }
        
        let duration = start_time.elapsed();
        info!("Retrieved {} roles for user {} (admin: {}) in {}ms", 
              roles.len(), auth_context.user_id, auth_context.is_super_admin(), duration.as_millis());
        
        let response = UserRolesResponse {
            roles,
            user_identity: auth_context.user_id.to_string(),
            tenant_id: tenant_id.clone(),
        };
        
        Ok(Json(ApiResponse::success(response, Some("获取用户角色成功".to_string()))))
    }
    
    /// 获取用户数据权限范围
    pub async fn get_user_data_scopes(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<DataScopeQueryParams>,
    ) -> Result<Json<ApiResponse<DataScopesResponse>>, StatusCode> {
        let start_time = std::time::Instant::now();
        
        // 确定租户ID
        let tenant_id = if let Some(tid) = params.tenant_id {
            tid
        } else {
            // 如果没有指定租户ID，使用用户的第一个租户
            if let Some(first_role) = auth_context.roles.first() {
                first_role.tenant_id.map(|id| id.to_string()).unwrap_or_else(|| "default".to_string())
            } else {
                "default".to_string()
            }
        };
        
        let resource = params.resource;
        
        debug!("Getting data scopes for user {} -> resource {} in tenant {}", 
               auth_context.user_id, resource, tenant_id);
        
        // 构建用户身份标识 (修复：使用正确的user:id格式)
        let user_identity = format!("user:{}", auth_context.user_id);
        
        // 获取用户数据权限范围
        let casbin_data_scopes = casbin_service
            .get_user_data_scopes(&user_identity, &tenant_id, &resource)
            .await
            .map_err(|e| {
                error!("Failed to get user data scopes: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;
        
        // 转换为API响应格式
        let data_scopes: Vec<DataScope> = casbin_data_scopes
            .into_iter()
            .map(|casbin_scope| DataScope {
                resource: casbin_scope.resource,
                scope_type: casbin_scope.scope_type,
                scope_value: casbin_scope.scope_value,
                actions: casbin_scope.actions,
            })
            .collect();
        
        let duration = start_time.elapsed();
        info!("Retrieved {} data scopes for user {} -> resource {} in {}ms", 
              data_scopes.len(), auth_context.user_id, resource, duration.as_millis());
        
        let response = DataScopesResponse {
            data_scopes,
            resource,
            user_identity: auth_context.user_id.to_string(),
            tenant_id: tenant_id.clone(),
        };
        
        Ok(Json(ApiResponse::success(response, Some("获取数据权限范围成功".to_string()))))
    }
    
    /// 根据菜单类型过滤菜单
    fn filter_menus_by_type(menus: Vec<MenuPermission>, menu_type: &str) -> Vec<MenuPermission> {
        match menu_type {
            "main" => {
                // 主菜单：排除个人中心和系统管理
                menus.into_iter()
                    .filter(|menu| !menu.menu_id.starts_with("personal_") && !menu.menu_id.starts_with("system_"))
                    .collect()
            }
            "admin" => {
                // 管理菜单：仅包含系统管理相关
                menus.into_iter()
                    .filter(|menu| menu.menu_id.starts_with("system_") || menu.menu_id.contains("management"))
                    .collect()
            }
            "personal" => {
                // 个人菜单：仅包含个人中心相关
                menus.into_iter()
                    .filter(|menu| menu.menu_id.starts_with("personal_") || menu.menu_id == "personal_center")
                    .collect()
            }
            _ => menus, // 未知类型返回所有菜单
        }
    }
    
    /// 在菜单树中查找指定ID的菜单
    fn find_menu_by_id<'a>(menus: &'a [MenuPermission], menu_id: &str) -> Option<&'a MenuPermission> {
        for menu in menus {
            if menu.menu_id == menu_id {
                return Some(menu);
            }
            
            // 递归查找子菜单
            if let Some(children) = &menu.children {
                if let Some(found) = Self::find_menu_by_id(children, menu_id) {
                    return Some(found);
                }
            }
        }
        
        None
    }
    
    /// 检查用户是否有访问指定菜单的权限
    async fn check_menu_permissions(
        casbin_service: &Arc<MultiTenantCasbinService>,
        auth_context: &crate::middleware::auth_middleware::AuthContext,
        tenant_id: &str,
        menu: &MenuPermission,
    ) -> Result<bool, StatusCode> {
        // 如果是系统管理员，直接允许
        if auth_context.is_super_admin() {
            return Ok(true);
        }
        
        // 构建用户身份标识 (修复：使用正确的user:id格式)
        let user_identity = format!("user:{}", auth_context.user_id);
        
        // 检查所需的每个权限
        for required_perm in &menu.required_permissions {
            let perm_parts: Vec<&str> = required_perm.split(':').collect();
            if perm_parts.len() >= 2 {
                let resource = perm_parts[0];
                let action = perm_parts[1];
                let object = if perm_parts.len() > 2 {
                    format!("{}:{}", resource, perm_parts[2])
                } else {
                    format!("{}:*", resource)
                };
                
                let permission_request = crate::service::permission::PermissionRequest {
                    subject: user_identity.clone(),
                    domain: tenant_id.to_string(),
                    object,
                    action: action.to_string(),
                };
                
                let allowed = casbin_service.enforce(&permission_request)
                    .await
                    .map_err(|e| {
                        error!("Permission check failed: {}", e);
                        StatusCode::INTERNAL_SERVER_ERROR
                    })?;
                
                if !allowed {
                    return Ok(false);
                }
            }
        }
        
        Ok(true)
    }
    
    /// 获取角色显示名称
    fn get_role_display_name(role_code: &str) -> String {
        match role_code {
            "super_admin" => "系统管理员".to_string(),
            "system_admin" => "系统管理员".to_string(),
            "tenant_admin" => "租户管理员".to_string(),
            "principal" => "校长".to_string(),
            "academic_director" => "教导主任".to_string(),
            "subject_leader" => "学科组长".to_string(),
            "grade_leader" => "年级长".to_string(),
            "class_teacher" => "班主任".to_string(),
            "teacher" => "任课老师".to_string(),
            "student" => "学生".to_string(),
            "parent" => "家长".to_string(),
            _ => role_code.to_string(),
        }
    }
    
    /// 获取角色级别
    fn get_role_level(role_code: &str) -> i32 {
        match role_code {
            "super_admin" => 1,
            "system_admin" => 2,
            "tenant_admin" => 3,
            "principal" => 10,
            "academic_director" => 20,
            "subject_leader" => 30,
            "grade_leader" => 30,
            "class_teacher" => 40,
            "teacher" => 50,
            "student" => 100,
            "parent" => 110,
            _ => 999,
        }
    }
    
    /// 获取角色类别
    fn get_role_category(role_code: &str) -> String {
        match role_code {
            "super_admin" | "system_admin" => "system".to_string(),
            "tenant_admin" => "tenant".to_string(),
            "principal" | "academic_director" => "school_management".to_string(),
            "subject_leader" | "grade_leader" => "department_management".to_string(),
            "class_teacher" | "teacher" => "teaching".to_string(),
            "student" => "student".to_string(),
            "parent" => "parent".to_string(),
            _ => "other".to_string(),
        }
    }
}

/// 创建菜单权限路由
pub fn create_router() -> Router<Arc<MultiTenantCasbinService>> {
    Router::new()
        .route("/menus", get(MenuPermissionController::get_user_menus))
        .route("/menus/check/{tenant_id}/{menu_id}", get(MenuPermissionController::check_menu_access))
        .route("/menus/batch-check", post(MenuPermissionController::batch_check_menu_access))
        .route("/roles", get(MenuPermissionController::get_user_roles))
        .route("/data-scopes", get(MenuPermissionController::get_user_data_scopes))
}