use crate::controller::administrative_classes::administrative_classes_controller;
use crate::controller::auth::auth_controller;
use crate::controller::classes::classes_controller;
use crate::controller::education_stage::education_stage_controller;
use crate::controller::grade::grade_controller;
use crate::controller::grading::grading_controller;
use crate::controller::homework::homework_controller;
use crate::controller::homework_students::homework_students_controller;
use crate::controller::question::question_type_controller;
use crate::controller::menu::menu_controller;
use crate::controller::permission::{menu_controller as permission_menu_controller, casbin_policy_controller, permission_template_controller};
use crate::controller::role::role_controller;
use crate::controller::student::student_controller;
use crate::controller::subject::subject_controller;
use crate::controller::subject_groups::subject_groups_controller;
use crate::controller::teacher::teacher_controller;
use crate::controller::teaching_aids::{teaching_aids_controller, textbook_paper_controller};
use crate::controller::teaching_classes::teaching_classes_controller;
use crate::controller::tenant::tenant_controller;
use crate::controller::user::{identity_controller, parent_controller, user_controller};
use crate::web_server::AppState;
use axum::Router;
use crate::controller::workflow::workflow_controller;
use crate::controller::paper::paper_controller;

pub fn create_protected_routes(app_state: &AppState) -> Router {
    Router::new()
        .nest(
            "/auth",
            auth_controller::create_router().with_state(app_state.auth_integration.auth_service()),
        )
        .nest(
            "/user",
            user_controller::crate_router().with_state(app_state.user_service.clone()),
        )
        .nest(
            "/identity",
            identity_controller::create_router()
                .with_state(app_state.auth_integration.identity_service()),
        )
        .nest(
            "/parent",
            parent_controller::create_router()
                .with_state(app_state.auth_integration.parent_service()),
        )
        .nest(
            "/tenants",
            tenant_controller::create_router()
                .with_state(app_state.auth_integration.tenant_service()),
        )
        .nest(
            "/roles",
            role_controller::create_router().with_state(app_state.role_service.clone()),
        )
        .nest(
            "/permissions",
            permission_menu_controller::create_router().with_state(app_state.casbin_service.clone()),
        )
        .nest(
            "/menus",
            menu_controller::create_router().with_state(app_state.menu_service.clone()),
        )
        .nest(
            "/casbin",
            casbin_policy_controller::create_router().with_state(app_state.db.clone()),
        )
        .nest(
            "/admin",
            permission_template_controller::create_router().with_state(app_state.db.clone()),
        )
        .nest(
            "/subjects",
            subject_controller::create_router().with_state(app_state.subject_service.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/students",
            student_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/teachers",
            teacher_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/classes",
            classes_controller::create_router().with_state(app_state.classes_service.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/administrativeClasses",
            administrative_classes_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/subjectGroups",
            subject_groups_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/teachingClasses",
            teaching_classes_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/grades",
            grade_controller::create_router().with_state(app_state.grade_service.clone()),
        )
        .nest(
            "/education-stages",
            education_stage_controller::create_router()
                .with_state(app_state.education_stage_service.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/homework",
            homework_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/homeworkStudents",
            homework_students_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/teaching-aids",
            teaching_aids_controller::create_router(app_state),
        )
        .nest(
            "/question-type",
            question_type_controller::create_router()
                .with_state(app_state.question_type_service.clone()),
        )
        .nest(
            "/grading/{tenant_name}",
            grading_controller::create_router().with_state(app_state.clone()),
        )
        .nest("/workflow",workflow_controller::create_router().with_state(app_state.workflow_service.clone()))
        .nest("/paper", paper_controller::create_router().with_state(app_state.paper_service.clone()))
        .nest("/textbook-paper", textbook_paper_controller::create_router().with_state(app_state.textbook_paper_service.clone()))
}

pub fn create_public_routes(app_state: &AppState) -> Router {
    Router::new().nest(
        "/auth",
        auth_controller::create_public_router()
            .with_state(app_state.auth_integration.auth_service()),
    )
}
