use std::sync::Arc;
use uuid::Uuid;
use sqlx::{PgPool, QueryBuilder, Postgres};
use tracing::{info, error};

use crate::{
    model::{FindAllStudentParams, Student},
    service::permission::{
        CasbinPermissionService, 
        OptimizedStudentDataFilter, 
        FilterContext,
        DataFilterManager,
    },
    utils::{
        error::AppError,
        schema::{connect_with_schema, validate_schema_name},
    },
};

/// 优化的学生服务
/// 集成了高效的 Casbin 权限过滤
#[derive(Clone)]
pub struct OptimizedStudentService {
    db_pool: PgPool,
    data_filter: Arc<OptimizedStudentDataFilter>,
    casbin_service: Arc<dyn CasbinPermissionService>,
}

impl OptimizedStudentService {
    pub fn new(
        db_pool: PgPool, 
        casbin_service: Arc<dyn CasbinPermissionService>
    ) -> Self {
        let data_filter = Arc::new(OptimizedStudentDataFilter::new(db_pool.clone()));
        
        Self {
            db_pool,
            data_filter,
            casbin_service,
        }
    }

    /// 分页查询学生列表（优化版本）
    pub async fn page_students_optimized(
        &self,
        schema_name: &str,
        params: &FindAllStudentParams,
        user_id: Uuid,
        tenant_id: String,
    ) -> Result<(Vec<Student>, i64), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        
        info!("Optimized student query for user: {}, tenant: {}, schema: {}", 
              user_id, tenant_id, safe_schema);

        // 构建基础查询
        let mut query_builder = QueryBuilder::new(format!(
            "SELECT s.* FROM {}.students s WHERE 1 = 1",
            safe_schema
        ));
        
        let mut count_builder = QueryBuilder::new(format!(
            "SELECT COUNT(*) FROM {}.students s WHERE 1 = 1",
            safe_schema
        ));

        // 创建过滤上下文
        let filter_context = FilterContext {
            user_id,
            tenant_id: tenant_id.clone(),
            user_identity: format!("user:{}", user_id),
            resource: "student".to_string(),
            action: "read".to_string(),
            schema_name: safe_schema.clone(),
        };

        // 应用权限过滤
        let filter_applied = self.data_filter
            .apply_filter_to_builders(
                &filter_context,
                &mut query_builder,
                &mut count_builder,
                self.casbin_service.as_ref(),
            )
            .await
            .map_err(|e| {
                error!("Failed to apply data filter: {}", e);
                AppError::DatabaseError(format!("Permission filter error: {}", e))
            })?;

        if filter_applied {
            info!("Applied permission filter for user: {}", user_id);
        }

        // 添加业务查询条件
        self.add_business_conditions(&mut query_builder, &mut count_builder, params);

        // 添加分页
        let page = params.page_params.get_page();
        let page_size = params.page_params.get_page_size();
        let offset = (page - 1) * page_size;

        query_builder
            .push(" ORDER BY s.created_at DESC LIMIT ")
            .push_bind(page_size as i64)
            .push(" OFFSET ")
            .push_bind(offset as i64);

        // 执行查询
        let students: Vec<Student> = query_builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| {
                error!("Failed to execute student query: {}", e);
                AppError::DatabaseError(e.to_string())
            })?;

        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| {
                error!("Failed to execute count query: {}", e);
                AppError::DatabaseError(e.to_string())
            })?;

        info!("Found {} students (total: {}) for user: {}", 
              students.len(), total, user_id);

        Ok((students, total))
    }

    /// 添加业务查询条件
    fn add_business_conditions(
        &self,
        query_builder: &mut QueryBuilder<Postgres>,
        count_builder: &mut QueryBuilder<Postgres>,
        params: &FindAllStudentParams,
    ) {
        // 姓名模糊查询
        if let Some(name_like) = &params.name_like {
            if !name_like.trim().is_empty() {
                query_builder
                    .push(" AND s.student_name ILIKE ")
                    .push_bind(format!("%{}%", name_like.trim()));
                count_builder
                    .push(" AND s.student_name ILIKE ")
                    .push_bind(format!("%{}%", name_like.trim()));
            }
        }

        // 学号精确查询
        if let Some(student_number) = &params.student_number {
            if !student_number.trim().is_empty() {
                query_builder
                    .push(" AND s.student_number = ")
                    .push_bind(student_number.trim());
                count_builder
                    .push(" AND s.student_number = ")
                    .push_bind(student_number.trim());
            }
        }

        // 手机号模糊查询
        if let Some(phone) = &params.phone {
            if !phone.trim().is_empty() {
                query_builder
                    .push(" AND s.phone ILIKE ")
                    .push_bind(format!("%{}%", phone.trim()));
                count_builder
                    .push(" AND s.phone ILIKE ")
                    .push_bind(format!("%{}%", phone.trim()));
            }
        }
    }

    /// 检查用户是否有访问特定学生的权限
    pub async fn check_student_access_permission(
        &self,
        user_id: Uuid,
        tenant_id: &str,
        student_id: Uuid,
        schema_name: &str,
    ) -> Result<bool, AppError> {
        // 首先查询学生所在的班级
        let class_query = format!(
            "SELECT administrative_class_id FROM {}.students WHERE id = $1",
            schema_name
        );
        
        let class_id: Option<Uuid> = sqlx::query_scalar(&class_query)
            .bind(student_id)
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        if let Some(class_id) = class_id {
            // 使用 Casbin 检查权限
            let permission_request = crate::service::permission::PermissionRequest {
                subject: format!("user:{}", user_id),
                domain: tenant_id.to_string(),
                object: format!("student:class:{}", class_id),
                action: "read".to_string(),
            };

            self.casbin_service
                .enforce(&permission_request)
                .await
                .map_err(|e| AppError::PermissionError(e.to_string()))
        } else {
            Ok(false)
        }
    }

    /// 批量检查学生访问权限
    pub async fn batch_check_student_access_permissions(
        &self,
        user_id: Uuid,
        tenant_id: &str,
        student_ids: &[Uuid],
        schema_name: &str,
    ) -> Result<Vec<bool>, AppError> {
        if student_ids.is_empty() {
            return Ok(Vec::new());
        }

        // 查询学生所在的班级
        let placeholders = student_ids.iter()
            .enumerate()
            .map(|(i, _)| format!("${}", i + 1))
            .collect::<Vec<_>>()
            .join(", ");

        let class_query = format!(
            "SELECT id, administrative_class_id FROM {}.students WHERE id IN ({})",
            schema_name, placeholders
        );

        let mut query = sqlx::query(&class_query);
        for student_id in student_ids {
            query = query.bind(student_id);
        }

        let rows = query
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))?;

        // 构建权限检查请求
        let mut permission_requests = Vec::new();
        let mut student_class_map = std::collections::HashMap::new();

        for row in rows {
            let student_id: Uuid = row.try_get("id")
                .map_err(|e| AppError::DatabaseError(e.to_string()))?;
            let class_id: Uuid = row.try_get("administrative_class_id")
                .map_err(|e| AppError::DatabaseError(e.to_string()))?;

            student_class_map.insert(student_id, class_id);

            permission_requests.push(crate::service::permission::PermissionRequest {
                subject: format!("user:{}", user_id),
                domain: tenant_id.to_string(),
                object: format!("student:class:{}", class_id),
                action: "read".to_string(),
            });
        }

        // 批量检查权限
        let permission_results = self.casbin_service
            .batch_enforce(&permission_requests)
            .await
            .map_err(|e| AppError::PermissionError(e.to_string()))?;

        // 构建结果映射
        let mut results = Vec::with_capacity(student_ids.len());
        for student_id in student_ids {
            if let Some(_class_id) = student_class_map.get(student_id) {
                // 找到对应的权限检查结果
                let index = student_class_map.keys()
                    .position(|&id| id == *student_id)
                    .unwrap_or(0);
                results.push(permission_results.get(index).copied().unwrap_or(false));
            } else {
                results.push(false);
            }
        }

        Ok(results)
    }

    /// 清除权限缓存
    pub async fn clear_permission_cache(&self, user_id: Option<Uuid>) {
        self.data_filter.clear_cache(user_id).await;
    }

    /// 获取用户可访问的班级列表
    pub async fn get_accessible_class_ids(
        &self,
        user_id: Uuid,
        tenant_id: &str,
        schema_name: &str,
    ) -> Result<Vec<Uuid>, AppError> {
        let filter_context = FilterContext {
            user_id,
            tenant_id: tenant_id.to_string(),
            user_identity: format!("user:{}", user_id),
            resource: "student".to_string(),
            action: "read".to_string(),
            schema_name: schema_name.to_string(),
        };

        // 使用数据过滤器获取可访问的班级
        self.data_filter
            .get_class_teacher_class_ids_cached(&filter_context)
            .await
            .map_err(|e| AppError::PermissionError(e.to_string()))
    }
}
