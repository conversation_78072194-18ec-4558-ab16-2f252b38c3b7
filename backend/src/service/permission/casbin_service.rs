use std::{
    collections::HashMap,
    sync::Arc,
};
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use casbin::{Enforcer, CoreApi, MgmtApi, RbacA<PERSON>, DefaultModel};
use dashmap::DashMap;
use sqlx::PgPool;
use uuid::Uuid;
use tracing::{info, debug, error};
use serde::{Serialize, Deserialize};
use super::postgres_adapter::PostgresAdapter;
use crate::service::menu::MenuService;
use crate::controller::menu::menu_controller::{DetailedMenuResponse, MenuQueryParams};

/// 权限检查请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionRequest {
    pub subject: String,      // 主体：user_id:role:target_id
    pub domain: String,       // 域：tenant_id  
    pub object: String,       // 对象：resource:scope:target_id
    pub action: String,       // 动作：read, write, create, delete, manage
}

/// 权限策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionPolicy {
    pub subject: String,
    pub domain: String, 
    pub object: String,
    pub action: String,
    pub effect: String,       // allow, deny
}

/// 角色关系
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleRelation {
    pub user: String,
    pub role: String,
    pub domain: String,
}

/// 菜单权限项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MenuPermission {
    pub menu_id: String,
    pub name: String,
    pub path: String,
    pub icon: Option<String>,
    pub parent_id: Option<String>,
    pub required_permissions: Vec<String>,
    pub data_scopes: Option<Vec<String>>,
    pub sort_order: Option<i32>,
    pub menu_type: String,
    pub children: Option<Vec<MenuPermission>>,
}

/// 数据范围权限
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataScope {
    pub resource: String,
    pub scope_type: String,   // class, grade, subject_group, school
    pub scope_value: String,  // 具体的范围值
    pub actions: Vec<String>, // 允许的操作
}

/// Casbin 权限服务特征
#[async_trait]
pub trait CasbinPermissionService: Send + Sync {
    /// 检查权限
    async fn enforce(&self, req: &PermissionRequest) -> Result<bool>;
    
    /// 批量检查权限
    async fn batch_enforce(&self, requests: &[PermissionRequest]) -> Result<Vec<bool>>;
    
    /// 添加权限策略
    async fn add_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    
    /// 删除权限策略
    async fn remove_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    
    /// 添加角色关系
    async fn add_role(&self, relation: &RoleRelation) -> Result<bool>;
    
    /// 删除角色关系
    async fn remove_role(&self, relation: &RoleRelation) -> Result<bool>;
    
    /// 获取用户在指定域的所有角色
    async fn get_roles_for_user(&self, user: &str, domain: &str) -> Result<Vec<String>>;
    
    /// 获取用户权限菜单
    async fn get_user_menus(&self, user_identity: &str, tenant_id: &str) -> Result<Vec<MenuPermission>>;
    
    /// 获取所有菜单（不进行权限过滤，用于super_admin）
    async fn get_all_menus(&self) -> Result<Vec<MenuPermission>>;
    
    /// 获取用户数据权限范围
    async fn get_user_data_scopes(&self, user_identity: &str, tenant_id: &str, resource: &str) -> Result<Vec<DataScope>>;
    
    /// 同步角色和权限数据
    async fn sync_permissions(&self, tenant_id: &str) -> Result<()>;
    
    /// 清除租户所有权限数据
    async fn clear_tenant_policies(&self, tenant_id: &str) -> Result<()>;
}

/// 多租户 Casbin 权限服务实现
pub struct MultiTenantCasbinService {
    /// 租户 enforcer 缓存
    enforcers: DashMap<String, Arc<Enforcer>>,
    /// 数据库连接池
    pool: PgPool,
    /// 模型文件路径
    model_path: String,
    /// 菜单服务，用于从数据库获取菜单权限
    menu_service: Arc<MenuService>,
}

impl MultiTenantCasbinService {
    /// 创建新的多租户 Casbin 服务
    pub async fn new(pool: PgPool, model_path: String, menu_service: Arc<MenuService>) -> Result<Self> {
        Ok(Self {
            enforcers: DashMap::new(),
            pool,
            model_path,
            menu_service,
        })
    }
    
    /// 获取或创建租户的 enforcer
    async fn get_tenant_enforcer(&self, tenant_id: &str) -> Result<Arc<Enforcer>> {
        // 检查缓存
        if let Some(enforcer) = self.enforcers.get(tenant_id) {
            debug!("Using cached enforcer for tenant: {}", tenant_id);
            return Ok(enforcer.clone());
        }
        
        // 创建新的 enforcer
        let enforcer = self.create_tenant_enforcer(tenant_id).await?;
        let enforcer_arc = Arc::new(enforcer);
        
        // 缓存 enforcer
        self.enforcers.insert(tenant_id.to_string(), enforcer_arc.clone());
        
        info!("Created new enforcer for tenant: {}", tenant_id);
        Ok(enforcer_arc)
    }
    
    /// 创建租户专用的 enforcer
    async fn create_tenant_enforcer(&self, tenant_id: &str) -> Result<Enforcer> {
        // 创建租户专用的 adapter
        let adapter = PostgresAdapter::new(self.pool.clone(), Some(tenant_id.to_string())).await?;

        // 创建默认模型
        let model = DefaultModel::from_file(&self.model_path).await?;

        // 创建 enforcer
        let mut enforcer = Enforcer::new(model, adapter).await?;

        // 启用日志
        enforcer.enable_log(true);

        // 启用自动保存
        enforcer.enable_auto_save(true);

        // 启用增量同步
        enforcer.enable_auto_build_role_links(true);

        debug!("Created enforcer for tenant: {} with model: {}", tenant_id, self.model_path);
        Ok(enforcer)
    }
    
    /// 从数据库加载菜单权限配置
    async fn load_menu_permissions(&self) -> Result<HashMap<String, MenuPermission>> {
        let mut menus = HashMap::new();

        // 从数据库获取菜单权限数据
        let default_params = MenuQueryParams {
            menu_type: None,
            parent_id: None,
            is_active: None,
            include_children: Some(true),
            include_metadata: Some(true),
            search: None,
            page: None,
            page_size: None,
        };
        match self.menu_service.get_menu_tree(&default_params).await {
            Ok(menu_tree) => {
                // 将菜单树转换为权限配置
                self.convert_menu_tree_to_permissions(&mut menus, menu_tree);
                debug!("Successfully loaded {} menu permissions from database", menus.len());
            }
            Err(e) => {
                error!("Failed to load menu permissions from database: {}", e);
                // 如果数据库加载失败，返回空的权限配置
                // 这样系统仍然可以运行，但菜单权限检查会失败
                return Err(anyhow!("Failed to load menu permissions: {}", e));
            }
        }

        Ok(menus)
    }

    /// 将菜单树转换为权限配置（用于缓存）
    fn convert_menu_tree_to_permissions(
        &self,
        menus: &mut HashMap<String, MenuPermission>,
        menu_tree: Vec<DetailedMenuResponse>
    ) {
        for menu in menu_tree {
            let menu_permission = MenuPermission {
                menu_id: menu.menu_id.clone(),
                name: menu.name.clone(),
                path: menu.path.clone(),
                icon: menu.icon.clone(),
                menu_type: menu.menu_type.clone(),
                parent_id: menu.parent_id.clone(),
                required_permissions: menu.required_permissions.clone(),
                data_scopes: menu.data_scopes.clone(),
                sort_order: Some(menu.sort_order),
                children: if let Some(children) = menu.children {
                    if children.is_empty() {
                        None
                    } else {
                        let mut child_permissions = Vec::new();
                        for child in children {
                            child_permissions.push(MenuPermission {
                                menu_id: child.menu_id.clone(),
                                name: child.name.clone(),
                                path: child.path.clone(),
                                icon: child.icon.clone(),
                                parent_id: child.parent_id.clone(),
                                required_permissions: child.required_permissions.clone(),
                                data_scopes: child.data_scopes.clone(),
                                sort_order: Some(child.sort_order),
                                menu_type: child.menu_type.clone(),
                                children: None, // 目前只支持两级菜单
                            });
                            // 也将子菜单添加到主映射中
                            menus.insert(child.menu_id.clone(), MenuPermission {
                                menu_id: child.menu_id.clone(),
                                name: child.name.clone(),
                                path: child.path.clone(),
                                icon: child.icon.clone(),
                                parent_id: child.parent_id.clone(),
                                required_permissions: child.required_permissions.clone(),
                                data_scopes: child.data_scopes.clone(),
                                sort_order: Some(child.sort_order),
                                menu_type: child.menu_type.clone(),
                                children: None,
                            });
                        }
                        Some(child_permissions)
                    }
                } else {
                    None
                },
            };

            menus.insert(menu.menu_id.clone(), menu_permission);
        }
    }

    /// 将已排序的菜单树转换为权限列表（保持排序）
    fn convert_sorted_menu_tree_to_permissions(
        &self,
        menu_tree: Vec<DetailedMenuResponse>
    ) -> Vec<MenuPermission> {
        let mut permissions = Vec::new();

        for menu in menu_tree {
            let menu_permission = MenuPermission {
                menu_id: menu.menu_id.clone(),
                name: menu.name.clone(),
                path: menu.path.clone(),
                icon: menu.icon.clone(),
                parent_id: menu.parent_id.clone(),
                menu_type: menu.menu_type.clone(),
                required_permissions: menu.required_permissions.clone(),
                data_scopes: menu.data_scopes.clone(),
                sort_order: Some(menu.sort_order),
                children: if let Some(children) = menu.children {
                    if children.is_empty() {
                        None
                    } else {
                        let mut child_permissions = Vec::new();
                        for child in children {
                            child_permissions.push(MenuPermission {
                                menu_id: child.menu_id.clone(),
                                name: child.name.clone(),
                                path: child.path.clone(),
                                icon: child.icon.clone(),
                                parent_id: child.parent_id.clone(),
                                required_permissions: child.required_permissions.clone(),
                                data_scopes: child.data_scopes.clone(),
                                sort_order: Some(child.sort_order),
                                menu_type: child.menu_type.clone(),
                                children: None, // 目前只支持两级菜单
                            });
                        }
                        Some(child_permissions)
                    }
                } else {
                    None
                },
            };

            permissions.push(menu_permission);
        }

        permissions
    }

    /// 构建用户身份标识
    fn build_user_identity(user_id: &Uuid, role_code: &str, target_type: &str, target_id: Option<&Uuid>) -> String {
        match target_id {
            Some(id) => format!("{}:{}:{}:{}", user_id, role_code, target_type, id),
            None => format!("{}:{}:{}", user_id, role_code, target_type),
        }
    }
    
    /// 过滤用户可访问的菜单
    fn filter_menus_by_permissions<'a>(
        &'a self,
        menus: Vec<MenuPermission>,
        user_identity: &'a str,
        tenant_id: &'a str,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Vec<MenuPermission>>> + Send + 'a>> {
        Box::pin(async move {
            let enforcer = self.get_tenant_enforcer(tenant_id).await?;
            let mut filtered_menus = Vec::new();
            
            for menu in menus {
                // 检查菜单所需权限
                let mut has_all_permissions = true;
                for required_perm in &menu.required_permissions {
                    let perm_parts: Vec<&str> = required_perm.split(':').collect();
                    if perm_parts.len() >= 2 {
                        let resource = perm_parts[0];
                        let action = perm_parts[1];
                        let object = if perm_parts.len() > 2 {
                            format!("{}:*", resource)
                        } else {
                            required_perm.clone()
                        };
                        
                        if !enforcer.enforce((user_identity, tenant_id, &object, action))? {
                            has_all_permissions = false;
                            break;
                        }
                    }
                }
                
                if has_all_permissions {
                    let mut filtered_menu = menu.clone();
                    // 递归过滤子菜单
                    if let Some(children) = menu.children {
                        let filtered_children = self.filter_menus_by_permissions(
                            children, user_identity, tenant_id
                        ).await?;
                        filtered_menu.children = if filtered_children.is_empty() {
                            None
                        } else {
                            Some(filtered_children)
                        };
                    }
                    filtered_menus.push(filtered_menu);
                }
            }
            
            Ok(filtered_menus)
        })
    }
    
    /// 清除租户的 enforcer 缓存
    pub async fn clear_tenant_cache(&self, tenant_id: &str) {
        self.enforcers.remove(tenant_id);
        info!("Cleared enforcer cache for tenant: {}", tenant_id);
    }
    
    /// 重新加载租户的权限策略
    pub async fn reload_tenant_policies(&self, tenant_id: &str) -> Result<()> {
        self.clear_tenant_cache(tenant_id).await;
        self.get_tenant_enforcer(tenant_id).await?;
        info!("Reloaded policies for tenant: {}", tenant_id);
        Ok(())
    }
    
    /// 获取租户的 enforcer（公开方法，用于策略管理）
    pub async fn get_tenant_enforcer_public(&self, tenant_id: &str) -> Result<Arc<Enforcer>> {
        self.get_tenant_enforcer(tenant_id).await
    }
}

#[async_trait]
impl CasbinPermissionService for MultiTenantCasbinService {
    async fn enforce(&self, req: &PermissionRequest) -> Result<bool> {
        let enforcer = self.get_tenant_enforcer(&req.domain).await?;
        
        let result = enforcer.enforce((
            &req.subject,
            &req.domain, 
            &req.object,
            &req.action,
        ))?;
        
        debug!(
            "Permission check: {} -> {} @ {} | {} -> {}",
            req.subject, req.object, req.domain, req.action, result
        );
        
        Ok(result)
    }
    
    async fn batch_enforce(&self, requests: &[PermissionRequest]) -> Result<Vec<bool>> {
        if requests.is_empty() {
            return Ok(vec![]);
        }
        
        // 按租户分组批量检查
        let mut results = Vec::with_capacity(requests.len());
        let mut tenant_groups: HashMap<String, Vec<(usize, &PermissionRequest)>> = HashMap::new();
        
        // 按租户分组
        for (idx, req) in requests.iter().enumerate() {
            tenant_groups
                .entry(req.domain.clone())
                .or_insert_with(Vec::new)
                .push((idx, req));
        }
        
        // 初始化结果向量
        results.resize(requests.len(), false);
        
        // 按租户批量检查
        for (tenant_id, tenant_requests) in tenant_groups {
            let enforcer = self.get_tenant_enforcer(&tenant_id).await?;
            
            for (idx, req) in tenant_requests {
                let result = enforcer.enforce((
                    &req.subject,
                    &req.domain,
                    &req.object, 
                    &req.action,
                ))?;
                results[idx] = result;
            }
        }
        
        Ok(results)
    }
    
    async fn add_policy(&self, policy: &PermissionPolicy) -> Result<bool> {
        let enforcer = self.get_tenant_enforcer(&policy.domain).await?;
        
        // 使用 Arc 的内部可变性需要通过 Mutex 或者重新设计
        // 对于 Casbin，我们需要重新获取 enforcer 来进行修改操作
        let mut new_enforcer = self.create_tenant_enforcer(&policy.domain).await?;
        
        let result = new_enforcer.add_policy(vec![
            policy.subject.clone(),
            policy.domain.clone(),
            policy.object.clone(),
            policy.action.clone(),
            policy.effect.clone(),
        ]).await?;
        
        if result {
            // 保存策略并更新缓存
            new_enforcer.save_policy().await?;
            let new_enforcer_arc = Arc::new(new_enforcer);
            self.enforcers.insert(policy.domain.clone(), new_enforcer_arc);
            
            info!("Added policy: {} -> {} @ {} | {}", 
                  policy.subject, policy.object, policy.domain, policy.action);
        }
        
        Ok(result)
    }
    
    async fn remove_policy(&self, policy: &PermissionPolicy) -> Result<bool> {
        let enforcer = self.get_tenant_enforcer(&policy.domain).await?;
        
        // 重新创建 enforcer 来进行修改操作
        let mut new_enforcer = self.create_tenant_enforcer(&policy.domain).await?;
        
        let result = new_enforcer.remove_policy(vec![
            policy.subject.clone(),
            policy.domain.clone(),
            policy.object.clone(),
            policy.action.clone(),
            policy.effect.clone(),
        ]).await?;
        
        if result {
            // 保存策略并更新缓存
            new_enforcer.save_policy().await?;
            let new_enforcer_arc = Arc::new(new_enforcer);
            self.enforcers.insert(policy.domain.clone(), new_enforcer_arc);
            
            info!("Removed policy: {} -> {} @ {} | {}",
                  policy.subject, policy.object, policy.domain, policy.action);
        }
        
        Ok(result)
    }
    
    async fn add_role(&self, relation: &RoleRelation) -> Result<bool> {
        // 重新创建 enforcer 来进行修改操作
        let mut new_enforcer = self.create_tenant_enforcer(&relation.domain).await?;
        
        let result = new_enforcer.add_role_for_user(&relation.user, &relation.role, Some(&relation.domain)).await?;
        
        if result {
            // 保存策略并更新缓存
            new_enforcer.save_policy().await?;
            let new_enforcer_arc = Arc::new(new_enforcer);
            self.enforcers.insert(relation.domain.clone(), new_enforcer_arc);
            
            info!("Added role: {} -> {} @ {}", 
                  relation.user, relation.role, relation.domain);
        }
        
        Ok(result)
    }
    
    async fn remove_role(&self, relation: &RoleRelation) -> Result<bool> {
        // 重新创建 enforcer 来进行修改操作
        let mut new_enforcer = self.create_tenant_enforcer(&relation.domain).await?;
        
        let result = new_enforcer.delete_role_for_user(&relation.user, &relation.role, Some(&relation.domain)).await?;
        
        if result {
            // 保存策略并更新缓存
            new_enforcer.save_policy().await?;
            let new_enforcer_arc = Arc::new(new_enforcer);
            self.enforcers.insert(relation.domain.clone(), new_enforcer_arc);
            
            info!("Removed role: {} -> {} @ {}",
                  relation.user, relation.role, relation.domain);
        }
        
        Ok(result)
    }
    
    async fn get_roles_for_user(&self, user: &str, domain: &str) -> Result<Vec<String>> {
        let enforcer = self.get_tenant_enforcer(domain).await?;
        let roles = enforcer.get_roles_for_user(user, Some(domain));
        Ok(roles)
    }
    
    async fn get_user_menus(&self, user_identity: &str, tenant_id: &str) -> Result<Vec<MenuPermission>> {
        // 直接从 MenuService 获取已排序的菜单树
        let default_params = MenuQueryParams {
            menu_type: None,
            parent_id: None,
            is_active: Some(true), // 只获取激活的菜单
            include_children: Some(true),
            include_metadata: Some(false), // 用户菜单不需要元数据
            search: None,
            page: None,
            page_size: None,
        };

        let sorted_menu_tree = self.menu_service.get_menu_tree(&default_params).await
            .map_err(|e| anyhow!("Failed to get menu tree: {}", e))?;
        let all_menus = self.convert_sorted_menu_tree_to_permissions(sorted_menu_tree);
        self.filter_menus_by_permissions(all_menus, user_identity, tenant_id).await
    }

    async fn get_all_menus(&self) -> Result<Vec<MenuPermission>> {
        // 直接从 MenuService 获取已排序的菜单树
        let default_params = MenuQueryParams {
            menu_type: None,
            parent_id: None,
            is_active: None, // 管理员可以看到所有菜单
            include_children: Some(true),
            include_metadata: Some(true),
            search: None,
            page: None,
            page_size: None,
        };

        let sorted_menu_tree = self.menu_service.get_menu_tree(&default_params).await
            .map_err(|e| anyhow!("Failed to get menu tree: {}", e))?;
        let all_menus = self.convert_sorted_menu_tree_to_permissions(sorted_menu_tree);
        Ok(all_menus)
    }
    
    async fn get_user_data_scopes(&self, user_identity: &str, tenant_id: &str, resource: &str) -> Result<Vec<DataScope>> {
        let enforcer = self.get_tenant_enforcer(tenant_id).await?;
        let mut data_scopes: Vec<DataScope> = Vec::new();
        
        // 获取用户的所有权限策略
        let policies = enforcer.get_filtered_policy(0, vec![user_identity.to_string()]);
        
        for policy in policies {
            if policy.len() >= 4 {
                let obj = &policy[2];
                let act = &policy[3];
                
                // 解析对象，格式：resource:scope:target_id 或 resource:scope
                let obj_parts: Vec<&str> = obj.split(':').collect();
                if obj_parts.len() >= 2 && obj_parts[0] == resource {
                    let scope_type = obj_parts[1].to_string();
                    let scope_value = if obj_parts.len() > 2 {
                        obj_parts[2].to_string()
                    } else {
                        "*".to_string()
                    };
                    
                    // 查找或创建数据范围
                    if let Some(existing_scope) = data_scopes.iter_mut().find(|s| {
                        s.scope_type == scope_type && s.scope_value == scope_value
                    }) {
                        if !existing_scope.actions.contains(&act.to_string()) {
                            existing_scope.actions.push(act.to_string());
                        }
                    } else {
                        data_scopes.push(DataScope {
                            resource: resource.to_string(),
                            scope_type,
                            scope_value,
                            actions: vec![act.to_string()],
                        });
                    }
                }
            }
        }
        
        Ok(data_scopes)
    }
    
    async fn sync_permissions(&self, tenant_id: &str) -> Result<()> {
        // 重新创建 enforcer 并重新加载策略
        let mut new_enforcer = self.create_tenant_enforcer(tenant_id).await?;
        
        // 重新加载策略
        new_enforcer.load_policy().await?;
        
        // 更新缓存
        let new_enforcer_arc = Arc::new(new_enforcer);
        self.enforcers.insert(tenant_id.to_string(), new_enforcer_arc);
        
        info!("Synced permissions for tenant: {}", tenant_id);
        Ok(())
    }
    
    async fn clear_tenant_policies(&self, tenant_id: &str) -> Result<()> {
        // 重新创建 enforcer
        let mut new_enforcer = self.create_tenant_enforcer(tenant_id).await?;
        
        // 清除所有策略
        let _= new_enforcer.clear_policy().await;
        
        // 保存到数据库
        new_enforcer.save_policy().await?;
        
        // 更新缓存
        let new_enforcer_arc = Arc::new(new_enforcer);
        self.enforcers.insert(tenant_id.to_string(), new_enforcer_arc);
        
        info!("Cleared all policies for tenant: {}", tenant_id);
        Ok(())
    }
}