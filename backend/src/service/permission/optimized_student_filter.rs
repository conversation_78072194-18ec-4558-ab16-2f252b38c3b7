use std::collections::HashMap;
use anyhow::Result;
use async_trait::async_trait;
use sqlx::{PgPool, QueryBuilder, Postgres};
use uuid::Uuid;
use tracing::{info, debug, warn};

use super::{
    DataFilter, FilterContext, FilterCondition, FilterParam,
    CasbinPermissionService, BaseDataFilter
};

/// 优化的学生数据过滤器
/// 结合 Casbin 权限和数据库查询优化
pub struct OptimizedStudentDataFilter {
    db_pool: PgPool,
    /// 缓存班主任-班级映射关系，减少数据库查询
    class_teacher_cache: tokio::sync::RwLock<HashMap<Uuid, Vec<Uuid>>>,
}

impl OptimizedStudentDataFilter {
    pub fn new(db_pool: PgPool) -> Self {
        Self {
            db_pool,
            class_teacher_cache: tokio::sync::RwLock::new(HashMap::new()),
        }
    }

    /// 获取班主任负责的班级ID列表（带缓存）
    async fn get_class_teacher_class_ids_cached(
        &self,
        context: &FilterContext,
    ) -> Result<Vec<Uuid>> {
        // 先检查缓存
        {
            let cache = self.class_teacher_cache.read().await;
            if let Some(class_ids) = cache.get(&context.user_id) {
                debug!("Cache hit for user_id: {}", context.user_id);
                return Ok(class_ids.clone());
            }
        }

        // 缓存未命中，查询数据库
        let class_ids = self.query_class_teacher_class_ids(context).await?;
        
        // 更新缓存
        {
            let mut cache = self.class_teacher_cache.write().await;
            cache.insert(context.user_id, class_ids.clone());
        }

        Ok(class_ids)
    }

    /// 从数据库查询班主任负责的班级ID
    async fn query_class_teacher_class_ids(
        &self,
        context: &FilterContext,
    ) -> Result<Vec<Uuid>> {
        info!("Querying class teacher class IDs for user_id: {} in schema: {}",
              context.user_id, context.schema_name);

        // 1. 根据user_id查找教师记录
        let teacher_query = format!(
            "SELECT id FROM {}.teachers WHERE user_id = $1 AND is_active = true",
            context.schema_name
        );
        
        let teacher_id: Option<Uuid> = sqlx::query_scalar(&teacher_query)
            .bind(context.user_id)
            .fetch_optional(&self.db_pool)
            .await?;

        if let Some(teacher_id) = teacher_id {
            // 2. 查找该教师负责的班级
            let class_query = format!(
                "SELECT id FROM {}.administrative_classes 
                 WHERE teacher_id = $1 AND is_active = true",
                context.schema_name
            );
            
            let class_ids: Vec<Uuid> = sqlx::query_scalar(&class_query)
                .bind(teacher_id)
                .fetch_all(&self.db_pool)
                .await?;

            info!("Found {} classes for teacher {}: {:?}", 
                  class_ids.len(), teacher_id, class_ids);
            
            Ok(class_ids)
        } else {
            warn!("No teacher record found for user_id: {}", context.user_id);
            Ok(Vec::new())
        }
    }

    /// 清除缓存（当班级分配发生变化时调用）
    pub async fn clear_cache(&self, user_id: Option<Uuid>) {
        let mut cache = self.class_teacher_cache.write().await;
        if let Some(user_id) = user_id {
            cache.remove(&user_id);
            info!("Cleared cache for user_id: {}", user_id);
        } else {
            cache.clear();
            info!("Cleared all class teacher cache");
        }
    }

    /// 构建高效的 SQL 过滤条件
    fn build_class_filter_condition(
        &self,
        class_ids: &[Uuid],
        table_alias: &str,
    ) -> (String, Vec<Uuid>) {
        if class_ids.is_empty() {
            return ("1 = 0".to_string(), Vec::new());
        }

        if class_ids.len() == 1 {
            (
                format!("{}.administrative_class_id = $", table_alias),
                vec![class_ids[0]]
            )
        } else {
            let placeholders = (0..class_ids.len())
                .map(|i| format!("${}", i + 1))
                .collect::<Vec<_>>()
                .join(", ");
            
            (
                format!("{}.administrative_class_id IN ({})", table_alias, placeholders),
                class_ids.to_vec()
            )
        }
    }
}

#[async_trait]
impl DataFilter for OptimizedStudentDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            debug!("User is system admin, no data filtering applied");
            return Ok(None);
        }

        // 2. 优先使用 Casbin 数据范围权限
        let data_scopes = casbin_service
            .get_user_data_scopes(&context.user_identity, &context.tenant_id, &context.resource)
            .await?;

        if let Some(condition) = BaseDataFilter::parse_data_scopes_to_condition(
            &data_scopes, &context.resource, "s"
        )? {
            info!("Using Casbin data scope condition for user: {}", context.user_id);
            return Ok(Some(condition));
        }

        // 3. 回退到基于角色的班级权限过滤
        let class_ids = self.get_class_teacher_class_ids_cached(context).await?;
        
        if !class_ids.is_empty() {
            let (condition_sql, params) = self.build_class_filter_condition(&class_ids, "s");
            
            let condition = FilterCondition {
                sql: condition_sql,
                params: params.into_iter().map(FilterParam::Uuid).collect(),
            };
            
            info!("Using class-based filtering for user: {}, classes: {:?}", 
                  context.user_id, class_ids);
            
            Ok(Some(condition))
        } else {
            // 无权限访问任何学生数据
            warn!("User {} has no class teacher permissions", context.user_id);
            Ok(Some(FilterCondition {
                sql: "1 = 0".to_string(),
                params: Vec::new(),
            }))
        }
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        info!("OptimizedStudentDataFilter::apply_filter_to_builders called for user_id: {}", 
              context.user_id);

        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            info!("User {} is system admin, no data filtering applied", context.user_id);
            return Ok(false);
        }

        // 2. 优先使用 Casbin 数据范围权限
        let data_scopes = casbin_service
            .get_user_data_scopes(&context.user_identity, &context.tenant_id, &context.resource)
            .await?;

        if !data_scopes.is_empty() {
            info!("Applying Casbin data scope filtering for user: {}", context.user_id);
            
            // 解析数据范围并应用到查询构建器
            for scope in &data_scopes {
                if scope.scope_type == "class" {
                    if let Ok(class_id) = scope.scope_value.parse::<Uuid>() {
                        query_builder.push(" AND s.administrative_class_id = ").push_bind(class_id);
                        count_builder.push(" AND s.administrative_class_id = ").push_bind(class_id);
                        return Ok(true);
                    }
                } else if scope.scope_type == "grade" {
                    // 处理年级范围权限
                    if let Ok(grade_id) = scope.scope_value.parse::<i32>() {
                        query_builder.push(" AND s.administrative_class_id IN (")
                            .push("SELECT id FROM ")
                            .push(&context.schema_name)
                            .push(".administrative_classes WHERE grade_level_id = ")
                            .push_bind(grade_id)
                            .push(")");
                        
                        count_builder.push(" AND s.administrative_class_id IN (")
                            .push("SELECT id FROM ")
                            .push(&context.schema_name)
                            .push(".administrative_classes WHERE grade_level_id = ")
                            .push_bind(grade_id)
                            .push(")");
                        return Ok(true);
                    }
                }
            }
        }

        // 3. 回退到基于角色的班级权限过滤
        let class_ids = self.get_class_teacher_class_ids_cached(context).await?;
        
        if !class_ids.is_empty() {
            info!("Applying class-based filtering for user: {}, classes: {:?}", 
                  context.user_id, class_ids);
            
            query_builder.push(" AND s.administrative_class_id IN (");
            count_builder.push(" AND s.administrative_class_id IN (");

            for (i, class_id) in class_ids.iter().enumerate() {
                if i > 0 {
                    query_builder.push(", ");
                    count_builder.push(", ");
                }
                query_builder.push_bind(class_id);
                count_builder.push_bind(class_id);
            }

            query_builder.push(")");
            count_builder.push(")");
            
            return Ok(true);
        }

        // 4. 无权限访问
        warn!("User {} has no permissions to access student data", context.user_id);
        query_builder.push(" AND 1 = 0");
        count_builder.push(" AND 1 = 0");
        Ok(true)
    }
}
