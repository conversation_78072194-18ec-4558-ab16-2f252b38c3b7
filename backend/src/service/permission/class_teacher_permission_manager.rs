use anyhow::{Result, anyhow};
use sqlx::PgPool;
use uuid::Uuid;
use tracing::{info, warn, error};
use serde::{Serialize, Deserialize};

use super::{CasbinPermissionService, PermissionPolicy, RoleRelation};

/// 班主任权限管理器
/// 专门处理班主任的学生数据访问权限
pub struct ClassTeacherPermissionManager {
    pool: PgPool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ClassTeacherPermission {
    pub user_id: Uuid,
    pub teacher_id: Uuid,
    pub class_id: Uuid,
    pub class_name: String,
    pub permissions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionSyncResult {
    pub success_count: usize,
    pub error_count: usize,
    pub errors: Vec<String>,
}

impl ClassTeacherPermissionManager {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 为班主任分配学生数据访问权限
    pub async fn assign_class_teacher_permissions(
        &self,
        tenant_id: &str,
        schema_name: &str,
        user_id: Uuid,
        class_ids: &[Uuid],
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<PermissionSyncResult> {
        let mut result = PermissionSyncResult {
            success_count: 0,
            error_count: 0,
            errors: Vec::new(),
        };

        let user_identity = format!("user:{}", user_id);

        // 1. 分配角色
        let role_relation = RoleRelation {
            user: user_identity.clone(),
            role: "role:class_teacher".to_string(),
            domain: tenant_id.to_string(),
        };

        if let Err(e) = casbin_service.add_role(&role_relation).await {
            let error_msg = format!("Failed to assign class_teacher role to user {}: {}", user_id, e);
            warn!("{}", error_msg);
            result.errors.push(error_msg);
            result.error_count += 1;
        } else {
            result.success_count += 1;
        }

        // 2. 为每个班级分配数据访问权限
        for &class_id in class_ids {
            // 学生数据读取权限
            let student_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("student:class:{}", class_id),
                action: "read".to_string(),
                effect: "allow".to_string(),
            };

            // 学生数据管理权限
            let student_manage_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("student:class:{}", class_id),
                action: "manage".to_string(),
                effect: "allow".to_string(),
            };

            // 班级数据访问权限
            let class_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("administrative_class:class:{}", class_id),
                action: "read".to_string(),
                effect: "allow".to_string(),
            };

            // 成绩数据访问权限
            let grade_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("grade:class:{}", class_id),
                action: "read".to_string(),
                effect: "allow".to_string(),
            };

            // 添加权限策略
            let policies = vec![
                student_policy,
                student_manage_policy,
                class_policy,
                grade_policy,
            ];

            for policy in policies {
                if let Err(e) = casbin_service.add_policy(&policy).await {
                    let error_msg = format!(
                        "Failed to add policy for user {} class {}: {} -> {}: {}",
                        user_id, class_id, policy.object, policy.action, e
                    );
                    warn!("{}", error_msg);
                    result.errors.push(error_msg);
                    result.error_count += 1;
                } else {
                    result.success_count += 1;
                }
            }
        }

        info!("Assigned permissions for user {}: {} success, {} errors", 
              user_id, result.success_count, result.error_count);

        Ok(result)
    }

    /// 移除班主任的学生数据访问权限
    pub async fn revoke_class_teacher_permissions(
        &self,
        tenant_id: &str,
        user_id: Uuid,
        class_ids: &[Uuid],
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<PermissionSyncResult> {
        let mut result = PermissionSyncResult {
            success_count: 0,
            error_count: 0,
            errors: Vec::new(),
        };

        let user_identity = format!("user:{}", user_id);

        // 移除每个班级的数据访问权限
        for &class_id in class_ids {
            let policies_to_remove = vec![
                format!("student:class:{}", class_id),
                format!("administrative_class:class:{}", class_id),
                format!("grade:class:{}", class_id),
            ];

            for object in policies_to_remove {
                for action in &["read", "manage"] {
                    let policy = PermissionPolicy {
                        subject: user_identity.clone(),
                        domain: tenant_id.to_string(),
                        object: object.clone(),
                        action: action.to_string(),
                        effect: "allow".to_string(),
                    };

                    if let Err(e) = casbin_service.remove_policy(&policy).await {
                        let error_msg = format!(
                            "Failed to remove policy for user {} class {}: {} -> {}: {}",
                            user_id, class_id, object, action, e
                        );
                        warn!("{}", error_msg);
                        result.errors.push(error_msg);
                        result.error_count += 1;
                    } else {
                        result.success_count += 1;
                    }
                }
            }
        }

        info!("Revoked permissions for user {}: {} success, {} errors", 
              user_id, result.success_count, result.error_count);

        Ok(result)
    }

    /// 同步所有班主任权限
    pub async fn sync_all_class_teacher_permissions(
        &self,
        tenant_id: &str,
        schema_name: &str,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<PermissionSyncResult> {
        info!("Starting sync of all class teacher permissions for tenant: {}", tenant_id);

        let mut total_result = PermissionSyncResult {
            success_count: 0,
            error_count: 0,
            errors: Vec::new(),
        };

        // 查询所有班主任和其负责的班级
        let query = format!(
            "SELECT t.user_id, t.id as teacher_id, ac.id as class_id, ac.class_name
             FROM {}.teachers t
             JOIN {}.administrative_classes ac ON t.id = ac.teacher_id
             WHERE t.is_active = true AND ac.is_active = true
             ORDER BY t.user_id",
            schema_name, schema_name
        );

        let rows = sqlx::query(&query)
            .fetch_all(&self.pool)
            .await?;

        // 按用户分组处理
        let mut user_classes: std::collections::HashMap<Uuid, Vec<Uuid>> = std::collections::HashMap::new();
        
        for row in rows {
            let user_id: Uuid = row.try_get("user_id")?;
            let class_id: Uuid = row.try_get("class_id")?;
            
            user_classes.entry(user_id).or_insert_with(Vec::new).push(class_id);
        }

        // 为每个班主任分配权限
        for (user_id, class_ids) in user_classes {
            match self.assign_class_teacher_permissions(
                tenant_id, 
                schema_name, 
                user_id, 
                &class_ids, 
                casbin_service
            ).await {
                Ok(result) => {
                    total_result.success_count += result.success_count;
                    total_result.error_count += result.error_count;
                    total_result.errors.extend(result.errors);
                },
                Err(e) => {
                    let error_msg = format!("Failed to sync permissions for user {}: {}", user_id, e);
                    error!("{}", error_msg);
                    total_result.errors.push(error_msg);
                    total_result.error_count += 1;
                }
            }
        }

        info!("Completed sync of all class teacher permissions: {} success, {} errors", 
              total_result.success_count, total_result.error_count);

        Ok(total_result)
    }

    /// 获取班主任的权限详情
    pub async fn get_class_teacher_permissions(
        &self,
        tenant_id: &str,
        schema_name: &str,
        user_id: Uuid,
    ) -> Result<ClassTeacherPermission> {
        // 查询教师信息和负责的班级
        let query = format!(
            "SELECT t.id as teacher_id, ac.id as class_id, ac.class_name
             FROM {}.teachers t
             JOIN {}.administrative_classes ac ON t.id = ac.teacher_id
             WHERE t.user_id = $1 AND t.is_active = true AND ac.is_active = true",
            schema_name, schema_name
        );

        let rows = sqlx::query(&query)
            .bind(user_id)
            .fetch_all(&self.pool)
            .await?;

        if rows.is_empty() {
            return Err(anyhow!("No class teacher record found for user: {}", user_id));
        }

        let first_row = &rows[0];
        let teacher_id: Uuid = first_row.try_get("teacher_id")?;
        let class_id: Uuid = first_row.try_get("class_id")?;
        let class_name: String = first_row.try_get("class_name")?;

        // 构建权限列表
        let permissions = vec![
            format!("student:class:{}", class_id),
            format!("administrative_class:class:{}", class_id),
            format!("grade:class:{}", class_id),
        ];

        Ok(ClassTeacherPermission {
            user_id,
            teacher_id,
            class_id,
            class_name,
            permissions,
        })
    }

    /// 验证班主任权限
    pub async fn verify_class_teacher_permissions(
        &self,
        tenant_id: &str,
        user_id: Uuid,
        class_id: Uuid,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        let user_identity = format!("user:{}", user_id);
        
        // 检查学生数据读取权限
        let permission_request = super::PermissionRequest {
            subject: user_identity,
            domain: tenant_id.to_string(),
            object: format!("student:class:{}", class_id),
            action: "read".to_string(),
        };

        casbin_service.enforce(&permission_request).await
    }
}
